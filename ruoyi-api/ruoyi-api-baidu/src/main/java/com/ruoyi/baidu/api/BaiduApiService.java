package com.ruoyi.baidu.api;

import com.alibaba.fastjson2.JSONException;
import com.baidubce.appbuilder.model.knowledgebase.Document;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.baidu.api.factory.BaiduApiFallbackFactory;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@FeignClient(contextId = "baiduApiService", value = ServiceNameConstants.BAIDU_API, fallbackFactory = BaiduApiFallbackFactory.class)
public interface BaiduApiService {

    /**
     * 创建数据集
     * @return 数据集版本ID
     */
    @PostMapping("/baidu/createData")
    public String createData(@RequestBody BaiduDto baiduDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 将文件上传到BOS
     * @return
     */
    @PostMapping("/baidu/putObjectSimple")
    public String[] putObjectSimple(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 导入数据集文件
     * @return 成功true 失败false
     */
    @PostMapping("/baidu/importDataFile")
    public Boolean importDataFile(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查看数据集导入状态
     * @return 数据集导入状态
     */
    @PostMapping("/baidu/importStatus")
    public int importStatus(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 发布数据集
     * @return 成功true 失败false
     */
    @PostMapping("/baidu/releaseDataSet")
    public Boolean releaseDataSet(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除BOS桶中文件夹或文件
     * @return
     */
    @PostMapping("/baidu/delbos")
    public boolean delbos(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除数据集
     * @return 成功true 失败false
     */
    @PostMapping("/baidu/deldata")
    public Boolean deldata(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 创建模型精调任务
     * @return 任务ID
     */
    @PostMapping("/baidu/modelFineTuningTask")
    public String modelFineTuningTask(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询训练任务执行状态
     * @return 任务运行状态
     */
    @PostMapping("/baidu/fineTuningTaskDetails")
    public String fineTuningTaskDetails(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新建模型版本
     * @return 模型版本id
     */
    @PostMapping("/baidu/newModelVersion")
    public String newModelVersion(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建知识库
     * @return 知识库id
     */
    @PostMapping("/baidu/creatdataSet")
    public String creatdataSet(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 导入文件到知识库
     * @return 文件id数组
     */
    @PostMapping("/baidu/importFile")
    public String[] importFile(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    @PostMapping("/baidu/getDocuments")
    public Document[] getDocuments(@RequestBody BaiduDto baiduDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除知识库文件
     * @return 成功失败
     */
    @PostMapping("/baidu/delDataSetFile")
    public boolean delDataSetFile(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 对话大模型
     * @param baiduDto
     * @return 模型返回内容
     */
    @PostMapping("/baidu/sendContent")
    public String sendContent(@RequestBody BaiduDto baiduDto, @RequestHeader(value = SecurityConstants.FROM_SOURCE, required = false) String source);

    /**
     * 对话知识库
     * @return 知识库返回内容
     */
    @PostMapping("/baidu/knowledgeBase")
    public String knowledgeBase(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建prompt优化任务
     * @return prompt优化任务id
     */
    @PostMapping("/baidu/insertPrompt")
    public String insertPromptOptimization(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取prompt优化任务详情
     * @return
     */
    @PostMapping("/baidu/getPromptInfo")
    public com.alibaba.fastjson2.JSONObject getPromptInfo(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 论文研读-新建会话
     * @return
     */
    @PostMapping("/baidu/newSession")
    public String newSession(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 论文研读-上传文件
     * @return
     */
    @PostMapping("/baidu/getfileId")
    public String getfileId(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 论文研读-获取回答
     * @return
     */
    @PostMapping("/baidu/getAnswer")
    public String getAnswer(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建知识库
     * @return 知识库id
     */
    @PostMapping("/baidu/creatdataSetNew")
    public String creatdataSetNew(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 导入文件到知识库 -新
     * @return 文件id数组
     */
    @PostMapping("/baidu/importFileNew")
    public String[] importFileNew(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 删除知识库文件 -新
     * @return 成功失败
     */
    @PostMapping("/baidu/delKnowledgeBaseFile")
    public boolean delKnowledgeBaseFile(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除知识库
     */
    @PostMapping("/baidu/delKnowledgeBase")
    public void delKnowledgeBase(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 问答-文生图
     * @return
     */
    @PostMapping("/baidu/getImage")
    public  Map<String,String> getImage(@RequestBody BaiduDto baiduDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/baidu/getImageInfo")
    public String getImageInfo(@RequestBody BaiduDto baiduDto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
