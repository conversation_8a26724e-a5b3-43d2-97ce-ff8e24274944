package com.ruoyi.baidu.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.baidubce.appbuilder.model.knowledgebase.Document;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public class BaiduApiFallbackFactory implements FallbackFactory<BaiduApiService> {
    private static final Logger log = LoggerFactory.getLogger(BaiduApiFallbackFactory.class);

    @Override
    public BaiduApiService create(Throwable throwable) {
        log.error("百度服务调用失败:{}", throwable.getMessage());
        return new BaiduApiService(){


            @Override
            public String createData(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String[] putObjectSimple(BaiduDto baiduDto, String source) {
                return new String[0];
            }

            @Override
            public Boolean importDataFile(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public int importStatus(BaiduDto baiduDto, String source) {
                return 0;
            }

            @Override
            public Boolean releaseDataSet(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public boolean delbos(BaiduDto baiduDto, String source) {
                return false;
            }

            @Override
            public Boolean deldata(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String modelFineTuningTask(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String fineTuningTaskDetails(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String newModelVersion(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String creatdataSet(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String[] importFile(BaiduDto baiduDto, String source) {
                return new String[0];
            }

            @Override
            public Document[] getDocuments(BaiduDto baiduDto, String source) {
                return new Document[0];
            }

            @Override
            public boolean delDataSetFile(BaiduDto baiduDto, String source) {
                return false;
            }

            @Override
            public String sendContent(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String knowledgeBase(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String insertPromptOptimization(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public JSONObject getPromptInfo(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String newSession(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String getfileId(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String getAnswer(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String creatdataSetNew(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String[] importFileNew(BaiduDto baiduDto, String source) {
                return new String[0];
            }

            @Override
            public boolean delKnowledgeBaseFile(BaiduDto baiduDto, String source) {
                return false;
            }

            @Override
            public void delKnowledgeBase(BaiduDto baiduDto, String source) {

            }

            @Override
            public  Map<String,String> getImage(BaiduDto baiduDto, String source) {
                return null;
            }

            @Override
            public String getImageInfo(BaiduDto baiduDto, String source) {
                return null;
            }
        };
    }
}
