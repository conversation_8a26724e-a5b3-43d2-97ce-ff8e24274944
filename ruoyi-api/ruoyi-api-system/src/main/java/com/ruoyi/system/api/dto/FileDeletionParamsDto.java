package com.ruoyi.system.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.file.domain
 * @Author: z<PERSON>_tian_qi
 * @CreateTime: 2024-11-20  11:19
 * @Description: 文件删除参数
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDeletionParamsDto {

	/**
	 * 文件路径列表
	 */
	List<String> filepathList;

	/**
	 * 延迟分钟
	 */
	Integer delayMinutes;
}
