package com.ruoyi.system.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.system.api.dto
 * @Author: <PERSON><PERSON>_tian_qi
 * @CreateTime: 2024-11-20  16:15
 * @Description: 内容写入到指定文件中
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextFileCreationDto {

	/**
	 * 文本内容
	 **/
	private String text;

	/**
	 * 文件路径
	 **/
	private String filePath;

	/** fileBytes **/
	private byte[] fileBytes;
}
