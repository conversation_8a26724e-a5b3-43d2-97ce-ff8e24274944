package com.ruoyi.system.vo;

import com.ruoyi.system.domain.KeySecretToken;
import com.ruoyi.system.domain.KeySecretTokenPermission;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

/**
 * key_secret_token vo
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KeySecretTokenVo extends KeySecretToken {
    private static final long serialVersionUID = 1L;

    private String searchValue;

    private List<KeySecretTokenPermission> keySecretTokenPermissionList;

    private Set<String> tokenPermissions;

}
