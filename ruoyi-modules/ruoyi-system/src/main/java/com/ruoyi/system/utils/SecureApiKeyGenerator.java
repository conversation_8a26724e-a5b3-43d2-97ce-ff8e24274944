package com.ruoyi.system.utils;

import java.security.SecureRandom;
import java.util.Base64;

public class SecureApiKeyGenerator {
    private static final SecureRandom random = new SecureRandom();
    private static final int KEY_LENGTH = 32;
    private static final int SECRET_LENGTH = 64;

    public static void main(String[] args) {
        String apiKey = generateSecureApiKey();
        String apiSecret = generateSecureApiSecret();

        System.out.println("API Key: " + apiKey);
        System.out.println("API Secret: " + apiSecret);
    }

    public static String generateSecureApiKey() {
        byte[] bytes = new byte[KEY_LENGTH];
        random.nextBytes(bytes);
        return "ak_" + Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }

    public static String generateSecureApiSecret() {
        byte[] bytes = new byte[SECRET_LENGTH];
        random.nextBytes(bytes);
        return "as_" + Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
}
