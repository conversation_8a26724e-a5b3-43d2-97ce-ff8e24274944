package com.ruoyi.system.domain.execl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Student {
    private Long userId;
    private Long deptId;
    private String studentId;

    private String sex;//
    private int sexMark;//

    private String studentName;

    private Long univerId;//
    private String univerName;//

    private Long colleId;//
    private String colleName;//
    private String colleNameSP;//


    private Long majorId;//
    private String majorName;//

    private Long classId;//
    private String className;//

    private int educationalSystem ;//学制
    private int currentGrade;// 当前所在级
    private int currentGradeSP;// 当前研究生所在级


    private int schoolStatus;// 在校状态
    private String schoolStatusStr;// 在校状态
    private int studentStatus;// 学籍状态
    private String studentStatusStr;// 学籍状态
    private int studentCurrentStatus;// 学生当前状态
    private String studentCurrentStatusStr;// 学生当前状态

    private String gradation;// 层次

    private String userType;//00
    private String authStatus;

    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}
