package com.ruoyi.system.domain.execl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Teacher {
    private Long userId;

    private Long deptId;
    private String deptName;

    private Long teacherId;
    private String teacherName;

    private Long univerId;
    private String univerName;
    private Long colleId;
    private String colleName;

    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}
