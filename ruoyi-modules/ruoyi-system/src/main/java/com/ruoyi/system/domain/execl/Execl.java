package com.ruoyi.system.domain.execl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Execl {
    private String filePath;

    private int isStu;// 0学生 1教师

    private int isAutoRegister;// 0自动注册 （导入stu/tea  和user） 1否 （只导入stu/tea）

    private int isAutoAuth;// 0自动认证 1否（导入user 认证状态 关联角色 ）

}
