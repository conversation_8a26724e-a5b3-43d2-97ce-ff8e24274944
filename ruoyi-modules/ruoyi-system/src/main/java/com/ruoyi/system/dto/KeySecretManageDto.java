package com.ruoyi.system.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * key_secret管理对象 dto
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeySecretManageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** key */
    @Excel(name = "key")
    private String apiKey;

    /** secretKey */
    @Excel(name = "secretKey")
    private String secretKey;

    /** 有效期 */
    @Excel(name = "有效期")
    private String expired;

    /** 客户 */
    @Excel(name = "客户")
    private String client;

}
