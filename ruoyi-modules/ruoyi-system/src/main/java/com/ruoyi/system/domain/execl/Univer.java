package com.ruoyi.system.domain.execl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Univer {
    private Long id;
    private String univerName;

    private String univerAddr;
    private String contact;
    private String phone;

    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}
