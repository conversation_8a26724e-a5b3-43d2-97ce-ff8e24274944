package com.ruoyi.system.vo;

import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysUserPartial1Vo implements Serializable {
    private static final long serialVersionUID = 1L;

    
    /** 用户账号 */
    @Excel(name = "登录名称")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    private String email;

    /** 手机号码 */
    @Excel(name = "手机号码", cellType = Excel.ColumnType.TEXT)
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;
    
}
