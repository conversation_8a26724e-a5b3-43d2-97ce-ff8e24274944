package com.ruoyi.job.service;

import com.ruoyi.job.domain.AutheNticationInfor;
import com.ruoyi.job.mapper.AutheNticationInforMapper;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

@Service
public class AutheNticationInforServiceImpl implements IAutheNticationInforService {

    @Autowired
    private AutheNticationInforMapper autheNticationInforMapper;
    private String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu";
    @Override
    public List<Map<String, String>> selectAutheNticationInforAll() {
        List<AutheNticationInfor> autheNticationInforList =  autheNticationInforMapper.selectAutheNticationInforAll();

        List<Map<String,String>> mapList = new ArrayList<>();
        for (AutheNticationInfor authenInfor : autheNticationInforList) {
            try {
                Map<String, String> map = new HashMap<>();
                map.put("menuRouting", authenInfor.getMenuRouting());
                map.put("apiKey", decrypt(authenInfor.getApiKey()));
                map.put("secretKey", decrypt(authenInfor.getSecretKey()));
                map.put("apiUrl", decrypt(authenInfor.getApiUrl()));
                map.put("ak", decrypt(authenInfor.getAk()));
                map.put("sk", decrypt(authenInfor.getSk()));
                map.put("domainName", decrypt(authenInfor.getDomainName()));
                map.put("bosBucketName", decrypt(authenInfor.getBosBucketName()));
                mapList.add(map);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if(mapList.size()>0){
            // 去重
            mapList = removeDuplicates(mapList);
        }
        return mapList;
    }
    public String decrypt(String str) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }
    private List<Map<String, String>> removeDuplicates(List<Map<String, String>> list) {
        Map<String, Map<String, String>> seen = new LinkedHashMap<>();
        list.forEach(map -> {
            String key = generateKey(map); // 生成唯一键
            if (!seen.containsKey(key)) {
                seen.put(key, map);
            }
        });

        return new ArrayList<>(seen.values());
    }

    private String generateKey(Map<String, String> map) {
        // 根据apiKey、secretKey和apiUrl生成唯一键
        return map.get("apiKey") + ":" + map.get("secretKey") + ":" + map.get("apiUrl");
    }
}
