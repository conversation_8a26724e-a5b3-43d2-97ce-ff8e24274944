package com.ruoyi.job.task;

import com.ruoyi.system.api.RemoteDateService;
import com.ruoyi.system.api.RemoteFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("CountMappingKnowledgeTask")
public class CountMappingKnowledgeTask {
    @Resource
    private RemoteDateService remoteDateService;
    public void cuntMappingKnowledgeTask() {

        remoteDateService.CountMappingKnowledge();

    }
}


