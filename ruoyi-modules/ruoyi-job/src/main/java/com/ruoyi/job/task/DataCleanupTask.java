package com.ruoyi.job.task;

import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUniversityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("DataCleanupTask")
public class DataCleanupTask {
    @Resource
    private RemoteFileService RemoteFileService;

    public void dataCleanup() {
        log.info("开始执行数据清理任务");
        RemoteFileService.fileClean();
        log.info("数据清理任务执行完成");
    }
}
