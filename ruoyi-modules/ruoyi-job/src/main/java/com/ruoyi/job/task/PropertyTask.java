package com.ruoyi.job.task;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.job.service.IAutheNticationInforService;
import com.ruoyi.system.api.RemoteAuthenInforService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

import okhttp3.RequestBody;
import okhttp3.*;

import java.util.List;

@Slf4j
@Component("propertyTask")
@RefreshScope
public class PropertyTask {
    /*@Autowired
    private RemoteAuthenInforService remoteAuthenInforService;*/
    @Autowired
    private IAutheNticationInforService autheNticationInforService;

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();
    public void timedTask(){
        log.info("执行定时任务对话");
        //0 0/30 0-23 * * ?    -每30分钟执行一次
        //0/20 * * * * ?   -测试20秒执行一次
        List<Map<String, String>> all = autheNticationInforService.selectAutheNticationInforAll();
        for (Map<String, String> stringStringMap : all) {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\"messages\":[{\"role\":\"user\",\"content\":\"介绍一下北京\"}]}");
            Request request = null;
            try {
                request = new Request.Builder()
                        .url(stringStringMap.get("apiUrl")+"?access_token=" + getAccessToken(stringStringMap))
                        .method("POST", body)
                        .addHeader("Content-Type", "application/json")
                        .build();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Response response = null;
            try {
                response = HTTP_CLIENT.newCall(request).execute();
                String string = response.body().string();
                JSONObject jsonObject = new JSONObject(string);
                String result = jsonObject.getString("result");
                System.out.println(result);
            } catch (IOException | JSONException e) {
                e.printStackTrace();
            }
        }

    }
    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public String getAccessToken(Map<String, String> stringStringMap) throws IOException, JSONException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" +stringStringMap.get("apiKey")
                + "&client_secret=" + stringStringMap.get("secretKey"));
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response);
        return new JSONObject(response.body().string()).getString("access_token");
    }

}
