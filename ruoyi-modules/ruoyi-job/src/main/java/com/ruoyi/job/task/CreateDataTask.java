package com.ruoyi.job.task;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.RemoteUniversityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.job.task
 * @Author: zhao_tian_qi
 * @CreateTime: 2024-12-11  14:38
 * @Description:
 * @Version: 1.0
 */
@Slf4j
@Component("createDataTask")
@RefreshScope
public class CreateDataTask {

	@Resource
	private RemoteUniversityService remoteUniversityService;

	public void dealAllCourseAnalysis() {
		AjaxResult result = remoteUniversityService.dealAllCourseAnalysis();
		if (result.get("code").toString().equals("200")) {
			log.info(result.toString());
		}else {
			log.error(result.get("msg").toString());
			throw new RuntimeException("处理dealAllCourseAnalysis失败：" + result.get("msg"));
		
		}
		
	}
	
}
