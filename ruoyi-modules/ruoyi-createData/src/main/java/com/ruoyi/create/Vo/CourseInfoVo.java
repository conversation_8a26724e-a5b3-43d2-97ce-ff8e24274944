package com.ruoyi.create.Vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class CourseInfoVo {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "课程时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scheduleTime;

    /** 课程周期 */
    @Excel(name = "课程周期")
    private String schedulePeriod;

    /** 课程描述 */
    @Excel(name = "课程描述")
    private String courseDescription;

    private String createUser;

    private String createTime;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCourseName(String courseName)
    {
        this.courseName = courseName;
    }

    public String getCourseName()
    {
        return courseName;
    }
    public void setScheduleTime(Date scheduleTime)
    {
        this.scheduleTime = scheduleTime;
    }

    public Date getScheduleTime()
    {
        return scheduleTime;
    }
    public void setSchedulePeriod(String schedulePeriod)
    {
        this.schedulePeriod = schedulePeriod;
    }

    public String getSchedulePeriod()
    {
        return schedulePeriod;
    }
    public void setCourseDescription(String courseDescription)
    {
        this.courseDescription = courseDescription;
    }

    public String getCourseDescription()
    {
        return courseDescription;
    }

    @Override
    public String toString() {
        return "CourseInfoVo{" +
                "id=" + id +
                ", courseName='" + courseName + '\'' +
                ", scheduleTime=" + scheduleTime +
                ", schedulePeriod='" + schedulePeriod + '\'' +
                ", courseDescription='" + courseDescription + '\'' +
                ", createUser='" + createUser + '\'' +
                ", createTime='" + createTime + '\'' +
                '}';
    }
}
