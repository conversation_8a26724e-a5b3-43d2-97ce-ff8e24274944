package com.ruoyi.create.Vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.create.domain.CollegeInfo;
import com.ruoyi.create.domain.MajorInfo;
import com.ruoyi.create.domain.University;
import com.ruoyi.create.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;



/**
 *
 * @Desoription
 * @Auther 摸鱼
 * @Date 2024-08-12
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PresentationVo implements Serializable{
    /**
     *
     *  id
     */
    private Long id;

    /**
     *
     *  学校
     */
    private String school;
    private University university;
    private String shoolName;

    /**
     *
     *  学院
     */
    private String college;
    private CollegeInfo collegeInfo;
    private String collageName;

    /**
     *
     *  专业
     */
    private String major;
    private MajorInfo majorInfo;
    private String majorName;
    /**
     *
     *  课程
     */
    private String course;

    /**
     *
     *  课件名称
     */
    private String presentationName;

    /**
     *
     *  课件id
     */
    private Long presentationId;

    /**
     *
     *  发布状态
     */
    private String presentationStatus;

    /**
     *
     *  创建人
     */
    private String createUser;

    /**
     *
     *  创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     *  修改人
     */
    private String updateBy;

    /**
     *
     *  修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     *
     *  讲演稿
     */
    private String speechdraftpath;

    /**
     *
     *  ppt页数
     */
    private Long presentationAllpage;

    /**
     *
     *  ppt未拆分路径
     */
    private String presentationPath;


    /**
     *
     *  grade
     */
    private String grade;


    private String presentationHttp;



    /** 未拆分相对的路径 */
    @Excel(name = "课件文件id")
    private String presentationFileId;


    /** 未拆分相对的路径 */
    @Excel(name = "讲演稿文件id")
    private String speechdraftFileId;



    @Excel(name = "所属单位")
    @TableField(exist = false)
    private Long[] affiliatedUnit;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "审核状态0未审核1通过2不通过")
    private int isExamine;

    @Excel(name = "审核建议")
    @TableField(exist = false)
    private String suggestion;

    @Excel(name = "当前返回数据是不是教务处获取的")
    @TableField(exist = false)
    private int authority;
    /** 章节 */
    @Excel(name = "章节")
    private String chapter;

    @Excel(name = "教材id")
    private String textBookId;

    @Excel(name = "教材id")
    private String textBookName;

    public String getTextBookName() {
        return textBookName;
    }

    public void setTextBookName(String textBookName) {
        this.textBookName = textBookName;
    }

    public String getTextBookId() {
        return textBookId;
    }

    public void setTextBookId(String textBookId) {
        this.textBookId = textBookId;
    }

    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }


    public int getAuthority() {
        return authority;
    }

    public void setAuthority(int authority) {
        this.authority = authority;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public int getIsExamine() {
        return isExamine;
    }

    public void setIsExamine(int isExamine) {
        this.isExamine = isExamine;
    }




    /** 是否联盟课 */
    private String isAllianceCourse;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    private List<Map<String, String>> presentationFileList;

    private List<Map<String, String>> speechdraftFileList;

    public List<Map<String, String>> getPresentationFileList() {
        return presentationFileList;
    }

    public void setPresentationFileList(List<Map<String, String>> presentationFileList) {
        this.presentationFileList = presentationFileList;
    }

    public List<Map<String, String>> getSpeechdraftFileList() {
        return speechdraftFileList;
    }

    public void setSpeechdraftFileList(List<Map<String, String>> speechdraftFileList) {
        this.speechdraftFileList = speechdraftFileList;
    }

    public Long[] getAffiliatedUnit() {
        return affiliatedUnit;
    }

    public void setAffiliatedUnit(Long[] affiliatedUnit) {
        this.affiliatedUnit = affiliatedUnit;
    }

    public String getPresentationFileId() {
        return presentationFileId;
    }

    public void setPresentationFileId(String presentationFileId) {
        this.presentationFileId = presentationFileId;
    }

    public String getSpeechdraftFileId() {
        return speechdraftFileId;
    }

    public void setSpeechdraftFileId(String speechdraftFileId) {
        this.speechdraftFileId = speechdraftFileId;
    }

    public University getUniversity() {
        return university;
    }

    public void setUniversity(University university) {
        this.university = university;
    }

    public String getShoolName() {
        return shoolName;
    }

    public void setShoolName(String shoolName) {
        this.shoolName = shoolName;
    }

    public String getCollageName() {
        return collageName;
    }

    public void setCollageName(String collageName) {
        this.collageName = collageName;
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public String getPresentationHttp() {
        return presentationHttp;
    }

    public void setPresentationHttp(String presentationHttp) {
        this.presentationHttp = presentationHttp;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public CollegeInfo getCollegeInfo() {
        return collegeInfo;
    }

    public void setCollegeInfo(CollegeInfo collegeInfo) {
        this.collegeInfo = collegeInfo;
    }

    public MajorInfo getMajorInfo() {
        return majorInfo;
    }

    public void setMajorInfo(MajorInfo majorInfo) {
        this.majorInfo = majorInfo;
    }

    public Long getPresentationAllpage() {
        return presentationAllpage;
    }

    public void setPresentationAllpage(Long presentationAllpage) {
        this.presentationAllpage = presentationAllpage;
    }

    public String getPresentationPath() {
        return presentationPath;
    }

    public void setPresentationPath(String presentationPath) {
        this.presentationPath = presentationPath;
    }

    public void setId(Long id) {
        this.id=id;
    }

    public Long getId() {
        return this.id;
    }

    public void setSchool(String school) {
        this.school=school;
    }

    public String getSchool() {
        return this.school;
    }

    public void setCollege(String college) {
        this.college=college;
    }

    public String getCollege() {
        return this.college;
    }

    public void setMajor(String major) {
        this.major=major;
    }

    public String getMajor() {
        return this.major;
    }

    public void setCourse(String course) {
        this.course=course;
    }

    public String getCourse() {
        return this.course;
    }

    public void setPresentationName(String presentationName) {
        this.presentationName=presentationName;
    }

    public String getPresentationName() {
        return this.presentationName;
    }

    public void setPresentationId(Long presentationId) {
        this.presentationId=presentationId;
    }

    public Long getPresentationId() {
        return this.presentationId;
    }

    public void setPresentationStatus(String presentationStatus) {
        this.presentationStatus=presentationStatus;
    }

    public String getPresentationStatus() {
        return this.presentationStatus;
    }

    public void setCreateUser(String createUser) {
        this.createUser=createUser;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateTime(Date createTime) {
        this.createTime=createTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy=updateBy;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime=updateTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setSpeechdraftpath(String speechdraftpath) {
        this.speechdraftpath=speechdraftpath;
    }

    public String getSpeechdraftpath() {
        return this.speechdraftpath;
    }

    public String getIsAllianceCourse() {
        return isAllianceCourse;
    }

    public void setIsAllianceCourse(String isAllianceCourse) {
        this.isAllianceCourse = isAllianceCourse;
    }

    @Override
    public String toString() {
        return " FieIdInfo{ " +
                " id : Id='" +(id==null ? "空" : id) + "' "+
                " 学校 : School='" +(school==null ? "空" : school) + "' "+
                " 学院 : College='" +(college==null ? "空" : college) + "' "+
                " 专业 : Major='" +(major==null ? "空" : major) + "' "+
                " 课程 : Course='" +(course==null ? "空" : course) + "' "+
                " 课件名称 : PresentationName='" +(presentationName==null ? "空" : presentationName) + "' "+
                " 课件id : PresentationId='" +(presentationId==null ? "空" : presentationId) + "' "+
                " 发布状态 : PresentationStatus='" +(presentationStatus==null ? "空" : presentationStatus) + "' "+
                " 创建人 : CreateUser='" +(createUser==null ? "空" : createUser) + "' "+
                " 创建时间 : CreateTime='" +(createTime==null ? "空" : DateUtils.format(createTime,"yyyy-MM-dd HH:mm:ss")) + "' "+
                " 修改人 : UpdateBy='" +(updateBy==null ? "空" : updateBy) + "' "+
                " 修改时间 : UpdateTime='" +(updateTime==null ? "空" : DateUtils.format(updateTime,"yyyy-MM-dd HH:mm:ss"))+ "' "+
                " 讲演稿 : Speechdraftpath='" +(speechdraftpath==null ? "空" : speechdraftpath) + "' "+ '}';
    }
}
