package com.ruoyi.create.Vo;/**
 * @Author: zhaoTianQi
 * @Description: TODO
 * @Date: 2024/10/30 15:21
 * @Version: 1.0
 */

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.create.Vo
 * @Author: zhaoTianQi
 * @CreateTime: 2024-10-30  15:21
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class SystemUserVo {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("部门ID")
    private Long deptId;

    @ApiModelProperty("用户账号（登录账号）")
    private String userName;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("用户类型（00系统用户）")
    private String userType;

    @ApiModelProperty("用户邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String phonenumber;

    @ApiModelProperty("用户性别（0男 1女 2未知）")
    private String sex;

    @ApiModelProperty("头像地址")
    private String avatar;
//
//    @ApiModelProperty("密码")
//    private String password;

    @ApiModelProperty("帐号状态（0正常 1停用）")
    private String status;

    @ApiModelProperty("删除标志（0代表存在 2代表删除）")
    private String delFlag;

    @ApiModelProperty("最后登录IP")
    private String loginIp;

    @ApiModelProperty("最后登录时间")
    private Date loginDate;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新者")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("身份证号")
    private String identityCard;

    @ApiModelProperty("认证状态（0未认证 1认证中 2认证完成 3认证失败）")
    private String authStatus;

    @ApiModelProperty("大学ID")
    private Long universityId;

    @ApiModelProperty("学院ID")
    private Long collegeId;

    @ApiModelProperty("专业ID")
    private Long majorId;

    @ApiModelProperty("班级ID")
    private Long classId;

    @ApiModelProperty("工号")
    private String jobId;

    @ApiModelProperty("学号")
    private String studentId;

    @ApiModelProperty("体验开始时间")
    private Date expTimeStart;

    @ApiModelProperty("体验结束时间")
    private Date expTimeEnd;


    @ApiModelProperty("研究成果")
    @TableField(exist = false)
    private String researchFindings;
    private List<String> researchFindingList;
}
