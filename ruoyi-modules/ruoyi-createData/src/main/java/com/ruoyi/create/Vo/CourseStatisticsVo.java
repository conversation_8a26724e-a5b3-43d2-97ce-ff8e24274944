package com.ruoyi.create.Vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CourseStatisticsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程名称")
    private String courseName;  // 课程名称

    @ApiModelProperty(value = "教师团队人数")
    private Integer teamMemberCount = 0;

    @ApiModelProperty(value = "副教授人数")
    private Integer associateProfessorCount = 0; // 副教授人数

    @ApiModelProperty(value = "讲师人数")
    private Integer lecturerCount = 0; // 讲师人数

    @ApiModelProperty(value = "教授人数")
    private Integer professorCount = 0; // 教授人数

    @ApiModelProperty(value = "其他人员人数")
    private Integer otherPersonnelCount = 0; // 其他人员人数

    @ApiModelProperty(value = "学生总人数")
    private Integer totalStudentCount = 0;  // 学生总人数

    @ApiModelProperty(value = "参与学习人数")
    private Integer participantCount = 0; // 参与学习人数


}
