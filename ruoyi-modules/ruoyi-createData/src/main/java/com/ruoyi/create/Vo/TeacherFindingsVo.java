package com.ruoyi.create.Vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 教师成果对象 s_teacher_findings
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class TeacherFindingsVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 老师ID */
    @Excel(name = "老师ID")
    private Long teacherId;

    /** 研究成果 */
    @Excel(name = "研究成果")
    private String researchFindings;

}
