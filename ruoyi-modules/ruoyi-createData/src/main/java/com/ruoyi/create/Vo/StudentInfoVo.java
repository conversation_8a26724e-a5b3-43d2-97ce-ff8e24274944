package com.ruoyi.create.Vo;/**
 * @Author: zhaoTianQi
 * @Description: TODO
 * @Date: 2024/11/5 16:17
 * @Version: 1.0
 */

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.create.Vo
 * @Author: zhaoTianQi
 * @CreateTime: 2024-11-05  16:17
 * @Description:
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StudentInfoVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 学校id
     */
    private Long univerId;
    private String univerName;

    /**
     * 学院id
     */
    private Long colleId;
    private String colleName;

    /**
     * 专业id
     */
    private Long majorId;
    private String majorName;

    /**
     * 班级id
     */
    private Long classId;
    private String className;

    /**
     * 学制
     */
    private Long educationalSystem;

    /**
     * 当前年级
     */
    private Long currentGrade;

    /**
     * 在校状态 0-离校  1-在校
     */
    private String schoolStatus;

    /**
     * 学籍状态  0-无学籍 1-有学籍
     */
    private String studentStatus;

    /**
     * 学生当前状态  0-休学  1-在读
     */
    private String studentCurrentStatus;
}
