package com.ruoyi.create.Vo;

import com.ruoyi.create.domain.DataDaping.*;

import java.io.Serializable;
import java.util.List;

/**
 * 大屏数据展示 VO，用于聚合展示所有统计模块的数据
 */
public class DataDapingVo implements Serializable {

    // 1. 智慧学堂统计（单条最新）
    private SWisdomStatistics wisdomStatistics;

    // 2. 平台实时数据（单条最新）
    private SPlatformReal platformReal;

    // 3. 专业知识点热度排行（多条）
    private List<SKnowledgeRanking> knowledgeRankingList;

    // 4. 每日趋势（多条，周一至周日）
    private List<SDailyTrend> dailyTrendList;

    // 5. 实践实训（多条）
    private List<SPracticalTraining> practicalTrainingList;

    // 6. 知识库课程数量（多条）
    private List<SCourseKnowledge> courseKnowledgeList;

    // 7. 学生活跃度排行（多条）
    private List<SStudentRanking> studentRankingList;

    // 8. 学生知识点掌握数量（多条）
    private List<SStudentAcquired> studentAcquiredList;

    // 9. 学生作业周完成情况（多条）
    private List<SWeeklyCompletion> weeklyCompletionList;

    // —— Getter & Setter 省略（可用 Lombok 简化） ——

    public SWisdomStatistics getWisdomStatistics() {
        return wisdomStatistics;
    }

    public void setWisdomStatistics(SWisdomStatistics wisdomStatistics) {
        this.wisdomStatistics = wisdomStatistics;
    }

    public SPlatformReal getPlatformReal() {
        return platformReal;
    }

    public void setPlatformReal(SPlatformReal platformReal) {
        this.platformReal = platformReal;
    }

    public List<SKnowledgeRanking> getKnowledgeRankingList() {
        return knowledgeRankingList;
    }

    public void setKnowledgeRankingList(List<SKnowledgeRanking> knowledgeRankingList) {
        this.knowledgeRankingList = knowledgeRankingList;
    }

    public List<SDailyTrend> getDailyTrendList() {
        return dailyTrendList;
    }

    public void setDailyTrendList(List<SDailyTrend> dailyTrendList) {
        this.dailyTrendList = dailyTrendList;
    }

    public List<SPracticalTraining> getPracticalTrainingList() {
        return practicalTrainingList;
    }

    public void setPracticalTrainingList(List<SPracticalTraining> practicalTrainingList) {
        this.practicalTrainingList = practicalTrainingList;
    }

    public List<SCourseKnowledge> getCourseKnowledgeList() {
        return courseKnowledgeList;
    }

    public void setCourseKnowledgeList(List<SCourseKnowledge> courseKnowledgeList) {
        this.courseKnowledgeList = courseKnowledgeList;
    }

    public List<SStudentRanking> getStudentRankingList() {
        return studentRankingList;
    }

    public void setStudentRankingList(List<SStudentRanking> studentRankingList) {
        this.studentRankingList = studentRankingList;
    }

    public List<SStudentAcquired> getStudentAcquiredList() {
        return studentAcquiredList;
    }

    public void setStudentAcquiredList(List<SStudentAcquired> studentAcquiredList) {
        this.studentAcquiredList = studentAcquiredList;
    }

    public List<SWeeklyCompletion> getWeeklyCompletionList() {
        return weeklyCompletionList;
    }

    public void setWeeklyCompletionList(List<SWeeklyCompletion> weeklyCompletionList) {
        this.weeklyCompletionList = weeklyCompletionList;
    }
}
