package com.ruoyi.create.Vo;


import lombok.Data;

@Data
public class StudentCourseAnalysisVo {
    // 学生课程分析统计
    String name; // 学生姓名
    String courseId; // 课程id
    String courseName; // 课程名字
    String reportDate; // 报告日期


    String totalChapterCount; // 总章节数
    String completedChapterCount;// 已完成章节数
    String completedChapterProgress; // 已完成章节进度
    String averageCompletionProgress; // 平均完成进度
    String comparisonWithClassAverageProgress; // 与班级平均进度比较

    String totalIntelligentQnACount; // 智能问答总次数
    String averageWeeklyQnACount; // 平均每周问答次数
    String comparisonWithClassAverageQnACount; // 与班级平均问答次数比较

    String totalHomeworkCount; // 总作业次数
    String submittedCount; // 已提交次数
    String submissionRate; // 提交比例
    String classAverageHomeworkCompletion; // 班级平均作业完成度
    String compareWithClassAverageHomeworkCompletion; // 与班级平均作业完成度比较

    String totalQuestions; // 总题目数
    String correctAnswersCount; // 正确题目数
    String accuracyRate; // 正确率
    String classAverageAccuracyRate; // 班级平均正确率
    String compareWithClassAverageAccuracyRate; // 与班级平均作业正确率比较

    String commonMistakeDistribution; // 易错点分布
    String learningProgressSuggestions; // 学习进度建议
    String questionFrequencySuggestions; // 提问频次建议
    String homeworkAccuracySuggestions; // 作业正确率建议
    String learningStrategySuggestions; // 学习策略建议


}
