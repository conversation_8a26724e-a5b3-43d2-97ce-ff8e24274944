package com.ruoyi.create.Vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.create.Vo
 * @Author: z<PERSON>_tian_qi
 * @CreateTime: 2024-12-02  20:55
 * @Description:
 * @Version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CourseAnalysisVo {

	// 昵称
	@ApiModelProperty("昵称")
	private String nickName;
	// 课程名
	@ApiModelProperty("课程名")
	private String courseName;
	//  报告日期
	@ApiModelProperty("报告日期")
	private String reportDate;
	// 总章节
	@ApiModelProperty("总章节")
	private String totalChapter;
	// 已完成章节
	@ApiModelProperty("已完成章节")
	private String finishedChapter;
	// 完成进度
	@ApiModelProperty("完成进度")
	private String completionProgress;
	// 平均完成进度
	@ApiModelProperty("平均完成进度")
	private String averageCompletionProgress;
	// 与平均进度比较
	@ApiModelProperty("与平均进度比较")
	private String comparison;
	// 智能问答次数
	@ApiModelProperty("智能问答次数")
	private String intelligentQuestionAnsweringCount;
	// 平均每周问答次数
	@ApiModelProperty("平均每周问答次数")
	private String averageWeeklyQuestionAnsweringCount;
	// 与班级平均问答次数比较
	@ApiModelProperty("与班级平均问答次数比较")
	private String comparisonWithClassAverageQuestionAnsweringCount;
	// 总作业数
	@ApiModelProperty("总作业数")
	private String totalHomework;
	// 已提交次数
	@ApiModelProperty("已提交次数")
	private String submittedTimes;
	// 提交比例
	@ApiModelProperty("提交比例")
	private String submissionRatio;

	// 建议
	// 学习进度
	@ApiModelProperty("学习进度 建议")
	private String learningProgressAdvice;
	// 提问频次建议
	@ApiModelProperty("提问频次建议")
	private String frequencyOfQuestionsAdvice;
	// 学习策略建议
	@ApiModelProperty("学习策略建议")
	private String learningStrategyAdvice;


	// 课程班级id
	@ApiModelProperty("课程班级id")
	private String classNameId;
	// 课程班级名称
	@ApiModelProperty("课程班级名称")
	private String className;
	// 进度大于 60% 比例
	@ApiModelProperty("进度大于 60% 比例")
	private String completionProgressGreaterThan60Percent;
	// 进度 大于 80% 比例
	@ApiModelProperty("进度 大于 80% 比例")
	private String completionProgressGreaterThan80Percent;
	// 进度 大于 90% 比例 
	@ApiModelProperty("进度 大于 90% 比例")
	private String completionProgressGreaterThan90Percent;
	// 作业提交大于 60% 比例
	@ApiModelProperty("作业提交大于 60% 比例")
	private String homeworkSubmissionGreaterThan60Percent;
	// 作业提交 大于 80% 比例
	@ApiModelProperty("作业提交 大于 80% 比例")
	private String homeworkSubmissionGreaterThan80Percent;
	// 作业提交 大于 90% 比例
	@ApiModelProperty("作业提交 大于 90% 比例")
	private String homeworkSubmissionGreaterThan90Percent;
	// 作业提交较低的学生
	@ApiModelProperty("作业完提交 较低的学生")
	private String lowHomeworkCompletionStudents;
	
	// 领先班级 问答次数 比例
	@ApiModelProperty("领先班级 问答次数 比例")
	private String aheadOfClassQuestionAnsweringRatio;
	// 问答次数前 3名学生
	@ApiModelProperty("问答次数前 3名学生")
	private String top3StudentsInQuestionAnswering;
	
	// 班级学习进度建议
	@ApiModelProperty("班级学习进度建议")
	private String classLearningProgressAdvice;
	// 班级作业提交建议
	@ApiModelProperty("班级作业提交建议")
	private String classHomeworkSubmissionAdvice;
	// 班级问答互动建议
	@ApiModelProperty("班级问答互动建议")
	private String classQuestionInteractionAdvice;
}
