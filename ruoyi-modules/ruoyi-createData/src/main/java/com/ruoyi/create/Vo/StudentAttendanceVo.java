package com.ruoyi.create.Vo;

import lombok.Data;
import org.joda.time.DateTime;

@Data
public class StudentAttendanceVo {

    private String id;
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 课程名
     */
    private String courseName;
    /**
     * 出勤时间
     */
    private DateTime attendanceTime;
    /**
     * 签到状态
     */
    private String status;
    /**
     * 签到方式
     */
    private String attendanceMethod;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建人姓名
     */
    private String createUserName;

    public StudentAttendanceVo() {
    }

    public StudentAttendanceVo(String id, String courseId, String courseName, DateTime attendanceTime, String status, String attendanceMethod, String createBy, String createUserName) {
        this.id = id;
        this.courseId = courseId;
        this.courseName = courseName;
        this.attendanceTime = attendanceTime;
        this.status = status;
        this.attendanceMethod = attendanceMethod;
        this.createBy = createBy;
        this.createUserName = createUserName;
    }
}
