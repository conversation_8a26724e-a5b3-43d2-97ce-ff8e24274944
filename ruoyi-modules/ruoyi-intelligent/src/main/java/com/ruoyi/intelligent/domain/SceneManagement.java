package com.ruoyi.intelligent.domain;

import com.ruoyi.system.api.domain.SysFileInfo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 场景管理对象 s_scene_management
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public class SceneManagement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 场景名称 */
    @Excel(name = "场景名称")
    private String sceneName;

    /** 场景图标 */
    @Excel(name = "场景图标")
    private String sceneIcon;

    /** 数字人形象ID */
    @Excel(name = "数字人形象ID")
    private Long figureId;

    /** 模型名称 */
    @Excel(name = "模型名称")
    private String modelName;

    /** 模型ak */
    @Excel(name = "模型ak")
    private String modelAk;

    /** 模型sk */
    @Excel(name = "模型sk")
    private String modelSk;

    /** 模型url */
    @Excel(name = "模型url")
    private String modelUrl;

    /** 知识库应用名称 */
    @Excel(name = "知识库应用名称")
    private String knowledgeBaseName;

    /** 知识库应用sk */
    @Excel(name = "知识库应用sk")
    private String knowledgeBaseSk;

    /** 知识库应用appid */
    @Excel(name = "知识库应用appid")
    private String knowledgeBaseAppid;

    /** 发音音量 */
    @Excel(name = "发音音量")
    private Long voiceVolume;

    /** 发音语调 */
    @Excel(name = "发音语调")
    private Long voicePitch;

    /** 发音语速 */
    @Excel(name = "发音语速")
    private Long voiceSpeed;

    /**
     * 发言人名称
     */
    private String voiceRoleName;

    /**
     * 百度发言人id
     */
    private String bdVoiceRoleId;

    /**
     * 讯飞发言人id
     */
    private String xfVoiceRoleId;


    private List<SysFileInfo> fileList;

    /**
     * 数字人预览图路径
     */
    private String previewUrl;

    /** 是否自动播放 */
    @Excel(name = "是否自动播放")
    private boolean userPlayflag;

    private Long[] fileIds;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSceneName(String sceneName) 
    {
        this.sceneName = sceneName;
    }

    public String getSceneName() 
    {
        return sceneName;
    }
    public void setSceneIcon(String sceneIcon) 
    {
        this.sceneIcon = sceneIcon;
    }

    public String getSceneIcon() 
    {
        return sceneIcon;
    }
    public void setFigureId(Long figureId) 
    {
        this.figureId = figureId;
    }

    public Long getFigureId() 
    {
        return figureId;
    }
    public void setModelName(String modelName) 
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }
    public void setModelAk(String modelAk) 
    {
        this.modelAk = modelAk;
    }

    public String getModelAk() 
    {
        return modelAk;
    }
    public void setModelSk(String modelSk) 
    {
        this.modelSk = modelSk;
    }

    public String getModelSk() 
    {
        return modelSk;
    }
    public void setModelUrl(String modelUrl) 
    {
        this.modelUrl = modelUrl;
    }

    public String getModelUrl() 
    {
        return modelUrl;
    }
    public void setKnowledgeBaseName(String knowledgeBaseName) 
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName() 
    {
        return knowledgeBaseName;
    }
    public void setKnowledgeBaseSk(String knowledgeBaseSk) 
    {
        this.knowledgeBaseSk = knowledgeBaseSk;
    }

    public String getKnowledgeBaseSk() 
    {
        return knowledgeBaseSk;
    }
    public void setKnowledgeBaseAppid(String knowledgeBaseAppid) 
    {
        this.knowledgeBaseAppid = knowledgeBaseAppid;
    }

    public String getKnowledgeBaseAppid() 
    {
        return knowledgeBaseAppid;
    }
    public void setVoiceVolume(Long voiceVolume) 
    {
        this.voiceVolume = voiceVolume;
    }

    public Long getVoiceVolume() 
    {
        return voiceVolume;
    }
    public void setVoicePitch(Long voicePitch) 
    {
        this.voicePitch = voicePitch;
    }

    public Long getVoicePitch() 
    {
        return voicePitch;
    }
    public void setVoiceSpeed(Long voiceSpeed) 
    {
        this.voiceSpeed = voiceSpeed;
    }

    public Long getVoiceSpeed() 
    {
        return voiceSpeed;
    }


    public String getVoiceRoleName() {
        return voiceRoleName;
    }

    public void setVoiceRoleName(String voiceRoleName) {
        this.voiceRoleName = voiceRoleName;
    }

    public String getBdVoiceRoleId() {
        return bdVoiceRoleId;
    }

    public void setBdVoiceRoleId(String bdVoiceRoleId) {
        this.bdVoiceRoleId = bdVoiceRoleId;
    }

    public String getXfVoiceRoleId() {
        return xfVoiceRoleId;
    }

    public void setXfVoiceRoleId(String xfVoiceRoleId) {
        this.xfVoiceRoleId = xfVoiceRoleId;
    }

    public boolean isUserPlayflag() {
        return userPlayflag;
    }

    public void setUserPlayflag(boolean userPlayflag) {
        this.userPlayflag = userPlayflag;
    }

    public Long[] getFileIds() {
        return fileIds;
    }

    public void setFileIds(Long[] fileIds) {
        this.fileIds = fileIds;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public List<SysFileInfo> getFileList() {
        return fileList;
    }

    public void setFileList(List<SysFileInfo> fileList) {
        this.fileList = fileList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sceneName", getSceneName())
            .append("sceneIcon", getSceneIcon())
            .append("figureId", getFigureId())
            .append("modelName", getModelName())
            .append("modelAk", getModelAk())
            .append("modelSk", getModelSk())
            .append("modelUrl", getModelUrl())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("knowledgeBaseSk", getKnowledgeBaseSk())
            .append("knowledgeBaseAppid", getKnowledgeBaseAppid())
            .append("voiceVolume", getVoiceVolume())
            .append("voicePitch", getVoicePitch())
            .append("voiceSpeed", getVoiceSpeed())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
