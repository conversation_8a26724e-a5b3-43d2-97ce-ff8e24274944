package com.ruoyi.intelligent.service.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Base64;
import java.util.List;

import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.intelligent.exception.CustomException;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.FileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import com.ruoyi.intelligent.mapper.SImgFormulaMapper;
import com.ruoyi.intelligent.domain.SImgFormula;
import com.ruoyi.intelligent.service.ISImgFormulaService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

/**
 * 公式图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
public class SImgFormulaServiceImpl implements ISImgFormulaService
{
    @Resource
    private SImgFormulaMapper sImgFormulaMapper;
    @Resource
    private RedisService redisService;

    @Resource
    private RemoteFileService remoteFileService;
    /**
     * 查询公式图片
     *
     * @param id 公式图片主键
     * @return 公式图片
     */
    @Override
    public SImgFormula selectSImgFormulaById(Long id)
    {
//        String keyword=redisService.getCacheObject("sImge:"+id);
//        if (keyword =="" || keyword == null){
//            SImgFormula sImgFormula = sImgFormulaMapper.selectSImgFormulaById(id);
//            String processing = null;
//            try {
//                processing = processing(sImgFormula.getImgPath());
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            sImgFormula.setImgBase("data:image/png;base64,"+processing);
//            return sImgFormula;
//        }
//        String path=redisService.getCacheObject("sImg:"+keyword+":path");
//        String name=redisService.getCacheObject("sImg:"+keyword+":name");
//        String explain=redisService.getCacheObject("sImg:"+keyword+":explain");
//        Integer mark=redisService.getCacheObject("sImg:"+keyword+":mark");


//        String processing2 = null;
//        try {
//            processing2 = processing(path);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        SImgFormula sImgFormula = sImgFormulaMapper.selectSImgFormulaById(id);
        return sImgFormula;
    }

    public String  processing(String filePath) throws IOException {
        File file = new File(filePath); // 图片的实际路径
        BufferedImage image = ImageIO.read(file);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, getFileExtension(filePath), baos);
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();

        return Base64.getEncoder().encodeToString(imageBytes);
    }
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ""; // 如果文件名为空，返回空字符串
        }
        int dotIndex = fileName.lastIndexOf('.'); // 查找最后一个'.'的位置
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // 如果没有找到'.'或者'.'在字符串末尾，说明没有扩展名
        }
        return fileName.substring(dotIndex + 1); // 返回'.'之后的部分，即文件扩展名
    }
    /**
     * 查询公式图片列表
     *
     * @param sImgFormula 公式图片
     * @return 公式图片
     */
    @Override
    public List<SImgFormula> selectSImgFormulaList(SImgFormula sImgFormula)
    {
        List<SImgFormula> sImgFormulaList = sImgFormulaMapper.selectSImgFormulaList(sImgFormula);
        sImgFormulaList.forEach(sImgFormula1 -> {
            if (sImgFormula1.getCreateBy()!=null && sImgFormula1.getCreateBy().equals(SecurityUtils.getUsername())){
                sImgFormula1.setIsCreateUser(0);
            }else {
                sImgFormula1.setIsCreateUser(1);
            }
//                if (StringUtils.isNotEmpty(sImgFormula1.getImgPath())) {
//                    String processing = processing(sImgFormula1.getImgPath());
//                    sImgFormula1.setImgBase("data:image/png;base64,"+processing);
//                }
        });
        return sImgFormulaList;
    }

    /**
     * 新增公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    @Override
    public int insertSImgFormula(SImgFormula sImgFormula)
    {
        List<SImgFormula> sImgFormulas = sImgFormulaMapper.selectSImgFormulaByName(sImgFormula.getImgName());
        List<SImgFormula> sImgFormulasReverse = sImgFormulaMapper.selectSImgFormulaByNameReverse(sImgFormula.getImgName());

        if (sImgFormulas.size() >= 1 || sImgFormulasReverse.size() >= 1){
            throw new CustomException("公式图片名字存在相似，请使用不同的公式图片名字。");
        }
        int i=0;
        try {
            i = sImgFormulaMapper.insertSImgFormula(sImgFormula);
            Long[] longs=new Long[1];
            longs[0]= Long.valueOf(sImgFormula.getFileId());
            remoteFileService.relationFile(longs, String.valueOf(sImgFormula.getId()));
        } catch (DataIntegrityViolationException e) {
            if (e.getCause() instanceof SQLIntegrityConstraintViolationException) {
                throw new CustomException("插入失败：关键字已存在，请使用不同的关键字。");
            }
            throw e; // 如果是其他异常则继续抛出
        }
        redisService.addElementsToSet("sImg"+sImgFormula.getImgMark(),sImgFormula.getImgName());

        return i;
    }

    /**
     * 修改公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    @Override
    public int updateSImgFormula(SImgFormula sImgFormula)
    {
        Long[] longs=new Long[1];
        longs[0]= Long.valueOf(sImgFormula.getFileId());
        remoteFileService.deleteFile(String.valueOf(sImgFormula.getId()));
        remoteFileService.relationFile(longs, String.valueOf(sImgFormula.getId()));
        return sImgFormulaMapper.updateSImgFormula(sImgFormula);
    }

    /**
     * 批量删除公式图片
     *
     * @param ids 需要删除的公式图片主键
     * @return 结果
     */
    @Override
    public int deleteSImgFormulaByIds(Long[] ids)
    {
        for (Long id:ids) {
            SImgFormula sImgFormula = sImgFormulaMapper.selectSImgFormulaById(id);
            redisService.removeElementFromSet("sImg"+sImgFormula.getImgMark(),sImgFormula.getImgName());
            remoteFileService.deleteFile(String.valueOf(id));
        }
        return sImgFormulaMapper.deleteSImgFormulaByIds(ids);
    }

    /**
     * 删除公式图片信息
     *
     * @param id 公式图片主键
     * @return 结果
     */
    @Override
    public int deleteSImgFormulaById(Long id)
    {
        SImgFormula sImgFormula = sImgFormulaMapper.selectSImgFormulaById(id);
        redisService.removeElementFromSet("sImg"+sImgFormula.getImgMark(),sImgFormula.getImgName());
        remoteFileService.deleteFile(String.valueOf(id));
        return sImgFormulaMapper.deleteSImgFormulaById(id);
    }


}
