package com.ruoyi.intelligent.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;


@Setter
@Configuration
@Getter
@Data
@RefreshScope
@Slf4j
public class DifyConfigProperties {

    // 本地配置
    @Value("${dify.base-url-1}")
    private String difyBaseUrl;

    @Value("${dify.app-api-key-1}")
    private String appApiKey1;

    @PostConstruct
    public void init() {
        log.info("当前配置：baseUrl={}, apiKey={}", difyBaseUrl, appApiKey1);
    }
}