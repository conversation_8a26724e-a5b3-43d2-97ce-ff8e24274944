package com.ruoyi.intelligent.service;

import java.util.List;
import com.ruoyi.intelligent.domain.SImgFormula;
import com.ruoyi.system.api.domain.FileVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 公式图片Service接口
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface ISImgFormulaService
{
    /**
     * 查询公式图片
     *
     * @param id 公式图片主键
     * @return 公式图片
     */
    public SImgFormula selectSImgFormulaById(Long id);

    /**
     * 查询公式图片列表
     *
     * @param sImgFormula 公式图片
     * @return 公式图片集合
     */
    public List<SImgFormula> selectSImgFormulaList(SImgFormula sImgFormula);

    /**
     * 新增公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    public int insertSImgFormula(SImgFormula sImgFormula);

    /**
     * 修改公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    public int updateSImgFormula(SImgFormula sImgFormula);

    /**
     * 批量删除公式图片
     *
     * @param ids 需要删除的公式图片主键集合
     * @return 结果
     */
    public int deleteSImgFormulaByIds(Long[] ids);

    /**
     * 删除公式图片信息
     *
     * @param id 公式图片主键
     * @return 结果
     */
    public int deleteSImgFormulaById(Long id);

}
