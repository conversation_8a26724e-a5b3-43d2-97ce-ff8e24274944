package com.ruoyi.intelligent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class ChatDifyFileDto {
    private String type; // 图片 image（目前仅支持图片格式）
    private String transferMethod ; //  remote_url: 图片地址 local_file: 上传文件
    private String url ; // 仅当传递方式为 remote_url 时
    private String uploadFileId; // （仅当传递方式为 local_file 时）。
}
