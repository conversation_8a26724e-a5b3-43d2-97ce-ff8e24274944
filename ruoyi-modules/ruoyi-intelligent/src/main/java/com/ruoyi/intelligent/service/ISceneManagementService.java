package com.ruoyi.intelligent.service;

import java.util.List;
import com.ruoyi.intelligent.domain.SceneManagement;

/**
 * 场景管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface ISceneManagementService 
{
    /**
     * 查询场景管理
     * 
     * @param id 场景管理主键
     * @return 场景管理
     */
    public SceneManagement selectSceneManagementById(Long id);

    /**
     * 查询场景管理列表
     * 
     * @param sceneManagement 场景管理
     * @return 场景管理集合
     */
    public List<SceneManagement> selectSceneManagementList(SceneManagement sceneManagement);

    /**
     * 新增场景管理
     * 
     * @param sceneManagement 场景管理
     * @return 结果
     */
    public int insertSceneManagement(SceneManagement sceneManagement);

    /**
     * 修改场景管理
     * 
     * @param sceneManagement 场景管理
     * @return 结果
     */
    public int updateSceneManagement(SceneManagement sceneManagement);

    /**
     * 批量删除场景管理
     * 
     * @param ids 需要删除的场景管理主键集合
     * @return 结果
     */
    public int deleteSceneManagementByIds(Long[] ids);

    /**
     * 删除场景管理信息
     * 
     * @param id 场景管理主键
     * @return 结果
     */
    public int deleteSceneManagementById(Long id);
}
