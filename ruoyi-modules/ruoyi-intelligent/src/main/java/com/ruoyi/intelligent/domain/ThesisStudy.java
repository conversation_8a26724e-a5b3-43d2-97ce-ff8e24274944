package com.ruoyi.intelligent.domain;

import com.ruoyi.system.api.domain.SysFileInfo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 论文研读对象 s_thesis_study
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public class ThesisStudy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 对话id */
    @Excel(name = "对话id")
    private String conversationId;

    /** 文件id */
    @Excel(name = "文件id")
    private String fileId;

    private String content;

    private Long[] fileIds;


    private List<ThesisDetails> thesisDetails;

    private List<SysFileInfo> sysFileInfoList;

    private String issue;
    private String language;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setConversationId(String conversationId)
    {
        this.conversationId = conversationId;
    }

    public String getConversationId()
    {
        return conversationId;
    }
    public void setFileId(String fileId)
    {
        this.fileId = fileId;
    }

    public String getFileId()
    {
        return fileId;
    }

    public Long[] getFileIds() {
        return fileIds;
    }

    public void setFileIds(Long[] fileIds) {
        this.fileIds = fileIds;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<ThesisDetails> getThesisDetails() {
        return thesisDetails;
    }

    public void setThesisDetails(List<ThesisDetails> thesisDetails) {
        this.thesisDetails = thesisDetails;
    }

    public List<SysFileInfo> getSysFileInfoList() {
        return sysFileInfoList;
    }

    public void setSysFileInfoList(List<SysFileInfo> sysFileInfoList) {
        this.sysFileInfoList = sysFileInfoList;
    }

    public String getIssue() {
        return issue;
    }

    public void setIssue(String issue) {
        this.issue = issue;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("conversationId", getConversationId())
                .append("fileId", getFileId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}