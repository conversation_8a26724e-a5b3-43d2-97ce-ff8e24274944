package com.ruoyi.intelligent.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 对话记录对象 s_dialogue_recording
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DialogueRecording extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public DialogueDetails DialogueDetails;
    public String content;
    private Long promptId;

    /**
     * 菜单路由
     */
    public String menuRouting;
    /**
     * 图片信息
     */
    private String processing;

    private String issue;
    /**
     * 调用
     */
    private String invocation;

    /**
     * 教学讲义
     */
    private String fullTextContent;

    /**
     *语言
     */
    private String language;


    private List<DialogueDetails> dialogueDetailsList;
    
    /**
     * 场景主题
     */
    private String sceneTheme;

    /**
     * 图生文图片路径
     */
    private String imagePath;
    /**
     * 文生图标志
     */
    private String imageFlag;

    /**
     * 是否深度
     */
    private Boolean deepThinking;


    private int txtToImage;

    private String difyConversationId; // 会话 ID，需要基于dify之前的聊天记录继续对话，必须传之前消息的 conversation_id。



}
