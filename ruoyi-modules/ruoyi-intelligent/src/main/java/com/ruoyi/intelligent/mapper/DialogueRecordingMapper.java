package com.ruoyi.intelligent.mapper;

import com.ruoyi.intelligent.domain.DialogueRecording;

import java.util.List;

/**
 * 对话记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface DialogueRecordingMapper 
{
    /**
     * 查询对话记录
     * 
     * @param id 对话记录主键
     * @return 对话记录
     */
    public DialogueRecording selectDialogueRecordingById(Long id);

    /**
     * 查询对话记录列表
     * 
     * @param dialogueRecording 对话记录
     * @return 对话记录集合
     */
    public List<DialogueRecording> selectDialogueRecordingList(DialogueRecording dialogueRecording);

    /**
     * 新增对话记录
     * 
     * @param dialogueRecording 对话记录
     * @return 结果
     */
    public int insertDialogueRecording(DialogueRecording dialogueRecording);

    /**
     * 修改对话记录
     * 
     * @param dialogueRecording 对话记录
     * @return 结果
     */
    public int updateDialogueRecording(DialogueRecording dialogueRecording);

    /**
     * 删除对话记录
     * 
     * @param id 对话记录主键
     * @return 结果
     */
    public int deleteDialogueRecordingById(Long id);

    /**
     * 批量删除对话记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDialogueRecordingByIds(Long[] ids);
}
