package com.ruoyi.intelligent.service.impl;

import java.util.List;

import com.ruoyi.intelligent.domain.DialogueDetails;
import com.ruoyi.intelligent.mapper.DialogueDetailsMapper;
import com.ruoyi.intelligent.service.IDialogueDetailsService;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;

/**
 * 对话记录明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class DialogueDetailsServiceImpl implements IDialogueDetailsService
{
    @Resource
    private DialogueDetailsMapper dialogueDetailsMapper;

    /**
     * 查询对话记录明细
     *
     * @param id 对话记录明细主键
     * @return 对话记录明细
     */
    @Override
    public List<DialogueDetails> selectDialogueDetailsById(Long id)
    {
        return dialogueDetailsMapper.selectDialogueDetailsById(id);
    }



    /**
     * 新增对话记录明细
     *
     * @param dialogueDetails 对话记录明细
     * @return 结果
     */
    @Override
    public int insertDialogueDetails(DialogueDetails dialogueDetails)
    {
        return dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
    }

    /**
     * 修改对话记录明细
     *
     * @param dialogueDetails 对话记录明细
     * @return 结果
     */
    @Override
    public int updateDialogueDetails(DialogueDetails dialogueDetails)
    {
        return dialogueDetailsMapper.updateDialogueDetails(dialogueDetails);
    }

    /**
     * 修改对话记录点赞点踩
     *
     * @return 结果
     */
    @Override
    public int updateLikeStompByDialogueIdAndOrderIn(DialogueDetails dialogueDetails)
    {
        return dialogueDetailsMapper.updateLikeStompByDialogueIdAndOrderIn(dialogueDetails);
    }


    /**
     * 批量删除对话记录明细
     *
     * @param ids 需要删除的对话记录明细主键
     * @return 结果
     */
    @Override
    public int deleteDialogueDetailsByIds(Long[] ids)
    {
        return dialogueDetailsMapper.deleteDialogueDetailsByIds(ids);
    }

    /**
     * 删除对话记录明细信息
     *
     * @param id 对话记录明细主键
     * @return 结果
     */
    @Override
    public int deleteDialogueDetailsById(Long id)
    {
        return dialogueDetailsMapper.deleteDialogueDetailsById(id);
    }
}
