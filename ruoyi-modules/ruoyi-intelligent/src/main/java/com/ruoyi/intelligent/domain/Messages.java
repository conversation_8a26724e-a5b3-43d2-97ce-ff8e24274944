package com.ruoyi.intelligent.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor()
public class Messages {
    private String role;
    private String content;
    private String sessionId;
    private String dialogueId;
    
    
    public Messages(String role, String content) {
        this.role = role;
        this.content = content;
    }

    @Override
    public String toString() {
        return "{" +
                "\"role\":\"" + role + '\"' +
                ", \"content\":\"" + content + '\"' +
                ", \"sessionId\":\"" + sessionId + '\"' +
                ", \"dialogueId\":\"" + dialogueId + '\"' +
                '}';
    }
}
