package com.ruoyi.intelligent.service.impl;

import com.ruoyi.intelligent.service.IXFSparkService;
import okhttp3.HttpUrl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;


/**
 * 作业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@RefreshScope
public class XFSparkServiceImpl implements IXFSparkService
{
    private Logger logger = LoggerFactory.getLogger(XFSparkServiceImpl.class);

    @Value("${dmxqy.xf-apiKey}")
    private String apiKey;
    @Value("${dmxqy.xf-secretKey}")
    private String apiSecret;

    // 设置连接超时和读取超时
    private static final int connectTimeout = 60; // 连接超时时长，单位为秒
    private static final int readTimeout = 60; // 读取超时时长，单位为秒


     @Override
     public String getAuthUrl(String hostUrl) throws Exception {
         hostUrl = hostUrl.replace("wss://", "https://").replace("ws://","http://");
         URL url = new URL(hostUrl);

         // 确保使用UTC时区和英文环境
         SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
         format.setTimeZone(TimeZone.getTimeZone("UTC"));
         String date = format.format(new Date());

         // 记录生成的时间用于调试
         logger.info("Generated date for auth: {}", date);

         StringBuilder builder = new StringBuilder("host: ").append(url.getHost()).append("\n")
                 .append("date: ").append(date).append("\n")
                 .append("GET ").append(url.getPath()).append(" HTTP/1.1");

         // 记录签名原文用于调试
         logger.info("Signature origin: {}", builder.toString());

         Charset charset = StandardCharsets.UTF_8;
         Mac mac = Mac.getInstance("HmacSHA256");
         SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "HmacSHA256");
         mac.init(spec);
         byte[] hexDigits = mac.doFinal(builder.toString().getBytes(charset));
         String sha = Base64.getEncoder().encodeToString(hexDigits);

         logger.info("Generated signature: {}", sha);

         String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                 apiKey, "hmac-sha256", "host date request-line", sha);

         HttpUrl httpUrl = HttpUrl.parse("https://" + url.getHost() + url.getPath()).newBuilder()
                 .addQueryParameter("authorization", Base64.getEncoder().encodeToString(authorization.getBytes(charset)))
                 .addQueryParameter("date", date)
                 .addQueryParameter("host", url.getHost())
                 .build();

         String finalUrl = httpUrl.toString();
         logger.info("Generated auth URL: {}", finalUrl);

         return finalUrl;
     }
}
