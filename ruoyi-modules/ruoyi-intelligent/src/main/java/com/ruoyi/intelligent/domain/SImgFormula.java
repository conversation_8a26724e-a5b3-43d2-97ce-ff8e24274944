package com.ruoyi.intelligent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 公式图片对象 s_img_formula
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SImgFormula extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    /** id */
    private String fileId;

    /** 图片名字/中英文标识为0  图片也是中文 同理标识1 */
    @Excel(name = "图片名字/中英文标识为0  图片也是中文 同理标识1")
    private String imgName;

    /** 图片path */
    @Excel(name = "图片path")
    private String imgPath;

    /** 图片说明 */
    @Excel(name = "图片说明")
    private String imgExplain;

//    /** 图片关键词/中英文标识为0  关键词也是中文 同理标识1 */
//    private String imgKeyword;

    /** 中英文标识0 中文/1 英文 */
    @Excel(name = "中英文标识0 中文/1 英文")
    private Integer imgMark;

    public Long getUniversityId() {
        return universityId;
    }

    public void setUniversityId(Long universityId) {
        this.universityId = universityId;
    }

    private Long universityId;

    private String imgBase;


    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private int isCreateUser;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public int getIsCreateUser() {
        return isCreateUser;
    }

    public void setIsCreateUser(int isCreateUser) {
        this.isCreateUser = isCreateUser;
    }

    public String getImgBase() {
        return imgBase;
    }

    public void setImgBase(String imgBase) {
        this.imgBase = imgBase;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setImgName(String imgName)
    {
        this.imgName = imgName;
    }

    public String getImgName()
    {
        return imgName;
    }
    public void setImgPath(String imgPath)
    {
        this.imgPath = imgPath;
    }

    public String getImgPath()
    {
        return imgPath;
    }
    public void setImgExplain(String imgExplain)
    {
        this.imgExplain = imgExplain;
    }

    public String getImgExplain()
    {
        return imgExplain;
    }
//    public void setImgKeyword(String imgKeyword)
//    {
//        this.imgKeyword = imgKeyword;
//    }
//
//    public String getImgKeyword()
//    {
//        return imgKeyword;
//    }
    public void setImgMark(Integer imgMark)
    {
        this.imgMark = imgMark;
    }

    public Integer getImgMark()
    {
        return imgMark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("imgName", getImgName())
            .append("imgPath", getImgPath())
            .append("imgExplain", getImgExplain())
//            .append("imgKeyword", getImgKeyword())
            .append("imgMark", getImgMark())
            .toString();
    }
}
