package com.ruoyi.intelligent.dto;


import com.ruoyi.intelligent.domain.DialogueDetails;
import com.ruoyi.intelligent.domain.DialogueRecording;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatDifyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String responseMode; // dify streaming 流式模式  blocking 阻塞模式
    private String difyConversationId; // dify 会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversation_id。
    private String difyMessageId; // dify 消息 ID，
    private List<ChatDifyFileDto> files; // dify 上传的文件

    private HashMap<String, String> inputs; // dify App 定义的各变量值
    private String difyTaskId; // dify的流式回复的 taskId 用于停止


    private String dialogueRecordingId;

    /**
     * 菜单路由
     */
    public String menuRouting;
    /**
     * 调用
     */
    private String invocation;

    private String content;

    private DialogueRecording dialogueRecording;

    private DialogueDetails dialogueDetails;

    private Boolean enableWs; // 开启ws发送

    private String wsId; // wsId

    private String medata;


    private String sessionId;



}


