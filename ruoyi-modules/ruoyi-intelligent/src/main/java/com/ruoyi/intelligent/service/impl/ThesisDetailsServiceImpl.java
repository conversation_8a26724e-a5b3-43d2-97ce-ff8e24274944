package com.ruoyi.intelligent.service.impl;

import java.util.List;

import com.ruoyi.intelligent.domain.ThesisDetails;
import com.ruoyi.intelligent.mapper.ThesisDetailsMapper;
import com.ruoyi.intelligent.service.IThesisDetailsService;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;

/**
 * 论文研读明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class ThesisDetailsServiceImpl implements IThesisDetailsService
{
    @Resource
    private ThesisDetailsMapper thesisDetailsMapper;

    /**
     * 查询论文研读明细
     *
     * @param id 论文研读明细主键
     * @return 论文研读明细
     */
    @Override
    public ThesisDetails selectThesisDetailsById(Long id)
    {
        return thesisDetailsMapper.selectThesisDetailsById(id);
    }

    /**
     * 查询论文研读明细列表
     *
     * @param thesisDetails 论文研读明细
     * @return 论文研读明细
     */
    @Override
    public List<ThesisDetails> selectThesisDetailsList(ThesisDetails thesisDetails)
    {
        return thesisDetailsMapper.selectThesisDetailsList(thesisDetails);
    }

    /**
     * 新增论文研读明细
     *
     * @param thesisDetails 论文研读明细
     * @return 结果
     */
    @Override
    public int insertThesisDetails(ThesisDetails thesisDetails)
    {
        return thesisDetailsMapper.insertThesisDetails(thesisDetails);
    }

    /**
     * 修改论文研读明细
     *
     * @param thesisDetails 论文研读明细
     * @return 结果
     */
    @Override
    public int updateThesisDetails(ThesisDetails thesisDetails)
    {
        return thesisDetailsMapper.updateThesisDetails(thesisDetails);
    }


    @Override
    public int updateLikeStompById(ThesisDetails thesisDetails)
    {
        return thesisDetailsMapper.updateLikeStompById(thesisDetails);
    }
    /**
     * 批量删除论文研读明细
     *
     * @param ids 需要删除的论文研读明细主键
     * @return 结果
     */
    @Override
    public int deleteThesisDetailsByIds(Long[] ids)
    {
        return thesisDetailsMapper.deleteThesisDetailsByIds(ids);
    }

    /**
     * 删除论文研读明细信息
     *
     * @param id 论文研读明细主键
     * @return 结果
     */
    @Override
    public int deleteThesisDetailsById(Long id)
    {
        return thesisDetailsMapper.deleteThesisDetailsById(id);
    }
}
