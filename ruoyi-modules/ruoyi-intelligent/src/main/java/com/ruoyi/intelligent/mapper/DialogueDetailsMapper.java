package com.ruoyi.intelligent.mapper;

import com.ruoyi.intelligent.domain.DialogueDetails;

import java.util.List;

/**
 * 对话记录明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface DialogueDetailsMapper
{
    /**
     * 查询对话记录明细
     *
     * @param id 对话记录明细主键
     * @return 对话记录明细
     */
    public List<DialogueDetails> selectDialogueDetailsById(Long id);

    /**
     * 查询对话记录明细列表
     *
     * @param id 对话记录明细
     * @return 对话记录明细集合
     */
    public List<DialogueDetails> selectDialogueDetailsList(Long id);

    /**
     * 新增对话记录明细
     *
     * @param dialogueDetails 对话记录明细
     * @return 结果
     */
    public int insertDialogueDetails(DialogueDetails dialogueDetails);

    /**
     * 修改对话记录明细
     *
     * @param dialogueDetails 对话记录明细
     * @return 结果
     */
    public int updateDialogueDetails(DialogueDetails dialogueDetails);

    public int updateLikeStompByDialogueIdAndOrderIn(DialogueDetails dialogueDetails);

    /**
     * 删除对话记录明细
     *
     * @param id 对话记录明细主键
     * @return 结果
     */
    public int deleteDialogueDetailsById(Long id);

    /**
     * 批量删除对话记录明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDialogueDetailsByIds(Long[] ids);

    Long selectMaxOrderById(Long id);

    List<DialogueDetails> selectDialogueDetailsListBySessionId(String sessionId);
}
