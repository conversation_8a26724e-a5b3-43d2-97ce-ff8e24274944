package com.ruoyi.intelligent.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 对话记录明细对象 s_dialogue_details
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
public class DialogueDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 对话记录ID */
    @Excel(name = "对话记录ID")
    private Long dialogueId;

    /** 次序 */
    @Excel(name = "次序")
    private Long orderIn;

    /** 发出人 user-模型 assistant-用户 */
    @Excel(name = "发出人 user-模型 assistant-用户")
    private String issue;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /**
     * 深度思考
     */
    private String reasoningContent;

    /** prompt */
    @Excel(name = "prompt")
    private String prompt;

    /** prompt */
    @Excel(name = "like_stomp")
    private int likeStomp;

    /**
     * 图片信息
     */
    private String processing;
   
    /**
     * 会话ID
     */
    private String sessionId;

    private Object difyMetaData;

    private String difyConversationId;
    
  
}
