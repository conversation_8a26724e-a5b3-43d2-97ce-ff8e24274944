package com.ruoyi.intelligent.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 论文研读明细对象 s_thesis_details
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public class ThesisDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 对话记录ID */
    @Excel(name = "对话记录ID")
    private Long thesisId;

    /** 次序 */
    @Excel(name = "次序")
    private Long orderIn;

    /** 发出人 user-模型 assistant-用户 */
    @Excel(name = "发出人 user-模型 assistant-用户")
    private String issue;

    /** 消息id */
    @Excel(name = "消息id")
    private String messageId;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** prompt */
    @Excel(name = "like_stomp")
    private int likeStomp;

    public int getLikeStomp() {
        return likeStomp;
    }

    public void setLikeStomp(int likeStomp) {
        this.likeStomp = likeStomp;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setThesisId(Long thesisId)
    {
        this.thesisId = thesisId;
    }

    public Long getThesisId()
    {
        return thesisId;
    }
    public void setOrderIn(Long orderIn)
    {
        this.orderIn = orderIn;
    }

    public Long getOrderIn()
    {
        return orderIn;
    }
    public void setIssue(String issue)
    {
        this.issue = issue;
    }

    public String getIssue()
    {
        return issue;
    }
    public void setMessageId(String messageId)
    {
        this.messageId = messageId;
    }

    public String getMessageId()
    {
        return messageId;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("thesisId", getThesisId())
            .append("orderIn", getOrderIn())
            .append("issue", getIssue())
            .append("messageId", getMessageId())
            .append("content", getContent())
            .toString();
    }
}
