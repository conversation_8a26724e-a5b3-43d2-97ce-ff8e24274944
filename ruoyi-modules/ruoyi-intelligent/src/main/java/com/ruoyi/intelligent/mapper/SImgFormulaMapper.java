package com.ruoyi.intelligent.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.intelligent.domain.SImgFormula;

/**
 * 公式图片Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface SImgFormulaMapper
{
    /**
     * 查询公式图片
     *
     * @param id 公式图片主键
     * @return 公式图片
     */
    public SImgFormula selectSImgFormulaById(Long id);

    /**
     * 查询公式图片列表
     *
     * @param sImgFormula 公式图片
     * @return 公式图片集合
     */
    public List<SImgFormula> selectSImgFormulaList(SImgFormula sImgFormula);

    /**
     * 新增公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    public int insertSImgFormula(SImgFormula sImgFormula);
    public List<SImgFormula> selectSImgFormulaByName(String imgName);
    public List<SImgFormula> selectSImgFormulaByNameReverse(String imgName);
    /**
     * 修改公式图片
     *
     * @param sImgFormula 公式图片
     * @return 结果
     */
    public int updateSImgFormula(SImgFormula sImgFormula);

    /**
     * 删除公式图片
     *
     * @param id 公式图片主键
     * @return 结果
     */
    public int deleteSImgFormulaById(Long id);

    /**
     * 批量删除公式图片
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSImgFormulaByIds(Long[] ids);


    List<SImgFormula> selectSImgFormulaListByImgName(Set<String> cacheSet);

    List<Long> selectAmbitByTeaId(String teacherId);

    String selectTeaIdByUserId(Long userId);
}
