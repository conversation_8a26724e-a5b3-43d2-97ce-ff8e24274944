package com.ruoyi.intelligent.mapper;

import com.ruoyi.intelligent.domain.ThesisStudy;

import java.util.List;

/**
 * 论文研读Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface ThesisStudyMapper 
{
    /**
     * 查询论文研读
     * 
     * @param id 论文研读主键
     * @return 论文研读
     */
    public ThesisStudy selectThesisStudyById(Long id);

    /**
     * 查询论文研读列表
     * 
     * @param thesisStudy 论文研读
     * @return 论文研读集合
     */
    public List<ThesisStudy> selectThesisStudyList(ThesisStudy thesisStudy);

    /**
     * 新增论文研读
     * 
     * @param thesisStudy 论文研读
     * @return 结果
     */
    public int insertThesisStudy(ThesisStudy thesisStudy);

    /**
     * 修改论文研读
     * 
     * @param thesisStudy 论文研读
     * @return 结果
     */
    public int updateThesisStudy(ThesisStudy thesisStudy);

    /**
     * 删除论文研读
     * 
     * @param id 论文研读主键
     * @return 结果
     */
    public int deleteThesisStudyById(Long id);

    /**
     * 批量删除论文研读
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteThesisStudyByIds(Long[] ids);
}
