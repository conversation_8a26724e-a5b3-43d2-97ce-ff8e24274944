package com.ruoyi.intelligent.service.impl;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.console.appbuilderclient.AppBuilderClient;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientResult;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.intelligent.domain.ThesisDetails;
import com.ruoyi.intelligent.domain.ThesisStudy;
import com.ruoyi.intelligent.mapper.ThesisDetailsMapper;
import com.ruoyi.intelligent.mapper.ThesisStudyMapper;
import com.ruoyi.intelligent.service.IThesisStudyService;
import com.ruoyi.intelligent.utils.Snowflake;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFileInfo;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

import okhttp3.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * 论文研读Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class ThesisStudyServiceImpl implements IThesisStudyService {
    @Resource
    private ThesisStudyMapper thesisStudyMapper;

    @Resource
    private ThesisDetailsMapper thesisDetailsMapper;

    @Resource
    private ConfigService configService;

    @Autowired
    private BaiduApiService baiduApiService;

    @Autowired
    private RemoteFileService remoteFileService;

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    /**
     * 查询论文研读
     *
     * @param id 论文研读主键
     * @return 论文研读
     */
    @Override
    public ThesisStudy selectThesisStudyById(Long id) {

        ThesisStudy thesisStudy = thesisStudyMapper.selectThesisStudyById(id);
        List<ThesisDetails> thesisDetails = thesisDetailsMapper.selectThesisDetailsListById(id);
        List<SysFileInfo> fileInfoListByWbryIds = remoteFileService.getFileInfoList(String.valueOf(id));
        thesisStudy.setThesisDetails(thesisDetails);
        thesisStudy.setSysFileInfoList(fileInfoListByWbryIds);
        return thesisStudy;
    }

    /**
     * 查询论文研读列表
     *
     * @param thesisStudy 论文研读
     * @return 论文研读
     */
    @Override
    public List<ThesisStudy> selectThesisStudyList(ThesisStudy thesisStudy) {
        thesisStudy.setCreateBy(SecurityUtils.getUsername());
        return thesisStudyMapper.selectThesisStudyList(thesisStudy);
    }

    /**
     * 新增论文研读
     *
     * @param thesisStudy 论文研读
     * @return 结果
     */
    @Override
    public ThesisStudy insertThesisStudy(ThesisStudy thesisStudy) throws Exception {
        if (StringUtils.isNull(thesisStudy.getFileIds()) || thesisStudy.getFileIds().length == 0) {
            throw new Exception("请先上传文件");
        }
        //新增文研读
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        thesisStudy.setId(id);
        thesisStudy.setCreateTime(DateUtils.getNowDate());
        thesisStudy.setCreateBy(SecurityUtils.getUsername());
        thesisStudyMapper.insertThesisStudy(thesisStudy);
       //关联文件
        if (ObjectUtils.isNotEmpty(thesisStudy.getFileIds())) {
            remoteFileService.relationFile(thesisStudy.getFileIds(),String.valueOf(id));
        }
        //获取文件路径
        List<SysFileInfo> sysFileInfoList = remoteFileService.getFileInfoList(String.valueOf(id));
        List<String> filePaths = sysFileInfoList.stream()
                .map(SysFileInfo::getFilePath)
                .collect(Collectors.toList());
        //创建会话
        String conversationId = newSession();
        //上传文件
        File file = new File(filePaths.get(0));
        String fileId = getfileId(conversationId, file);
        //会话
        String content = thesisStudy.getContent();
        String answer = getAnswer(content.replaceAll("\\r\\n|\\r|\\n", ""), conversationId, fileId);


        ThesisDetails thesisDetails = new ThesisDetails();
        thesisDetails.setOrderIn(1l);
        thesisDetails.setIssue("user");
        thesisDetails.setThesisId(id);
        thesisDetails.setContent(content);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);

        //增加对话记录明细
        thesisDetails.setOrderIn(2l);
        thesisDetails.setIssue("assistant");
        thesisDetails.setThesisId(id);
        thesisDetails.setContent(answer);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);

        thesisStudy.setConversationId(conversationId);
        thesisStudy.setFileId(fileId);
        thesisStudy.setContent(answer);
        thesisStudyMapper.updateThesisStudy(thesisStudy);

        return thesisStudy;
    }

    /**
     * 修改论文研读
     *
     * @param thesisStudy 论文研读
     * @return 结果
     */
    @Override
    public ThesisStudy updateThesisStudy(ThesisStudy thesisStudy) {
        Long id = thesisStudy.getId();
        ThesisStudy thesisStudy1 = thesisStudyMapper.selectThesisStudyById(id);
        String conversationId = thesisStudy1.getConversationId();
        String fileId = thesisStudy1.getFileId();
        String content = thesisStudy.getContent();
        String answer = getAnswer(content.replaceAll("\\r\\n|\\r|\\n", ""), conversationId, fileId);

        Long maxOrder = thesisDetailsMapper.selectMaxOrderById(id);
        ThesisDetails thesisDetails = new ThesisDetails();
        if (maxOrder != null) {
            thesisDetails.setOrderIn(maxOrder + 1);
        } else {
            thesisDetails.setOrderIn(1l);
        }
        thesisDetails.setId(id);
        thesisDetails.setIssue("user");
        thesisDetails.setThesisId(id);
        thesisDetails.setContent(content);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);

        //增加对话记录明细
        if (maxOrder != null) {
            thesisDetails.setOrderIn(maxOrder + 1);
        } else {
            thesisDetails.setOrderIn(2l);
        }
        thesisDetails.setIssue("assistant");
        thesisDetails.setThesisId(id);
        thesisDetails.setContent(answer);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);
        thesisStudy.setContent(answer);
        thesisStudy.setIssue("assistant");
        return thesisStudy;
    }

    /**
     * 批量删除论文研读
     *
     * @param ids 需要删除的论文研读主键
     * @return 结果
     */
    @Override
    public int deleteThesisStudyByIds(Long[] ids) {
        return thesisStudyMapper.deleteThesisStudyByIds(ids);
    }

    /**
     * 删除论文研读信息
     *
     * @param id 论文研读主键
     * @return 结果
     */
    @Override
    public int deleteThesisStudyById(Long id) {
        remoteFileService.deleteFile(String.valueOf(id));
        thesisDetailsMapper.deleteThesisDetailsById(id);
        return thesisStudyMapper.deleteThesisStudyById(id);
    }

    @Override
    public Flux<String> insertThesisStudyLiu(ThesisStudy thesisStudy) throws Exception {
        if (StringUtils.isNull(thesisStudy.getFileIds()) || thesisStudy.getFileIds().length == 0) {
            throw new Exception("请先上传文件");
        }
        //新增文研读
        long id = thesisStudy.getId();

        //关联文件
        if (ObjectUtils.isNotEmpty(thesisStudy.getFileIds())) {
            remoteFileService.relationFile(thesisStudy.getFileIds(),String.valueOf(thesisStudy.getId()));
        }
        //获取文件路径
        List<SysFileInfo> sysFileInfoList = remoteFileService.getFileInfoList(String.valueOf(thesisStudy.getId()));
        List<String> filePaths = sysFileInfoList.stream()
                .map(SysFileInfo::getFilePath)
                .collect(Collectors.toList());
        //创建会话
        String conversationId = newSession();
        //上传文件
        File file = new File(filePaths.get(0));
        String fileId = getfileId(conversationId, file);
        //会话
        thesisStudy.setConversationId(conversationId);
        thesisStudy.setFileId(fileId);
        thesisStudy.setCreateTime(DateUtils.getNowDate());
        thesisStudy.setCreateBy(SecurityUtils.getUsername());
        thesisStudyMapper.insertThesisStudy(thesisStudy);
        String content = thesisStudy.getContent();


        ThesisDetails thesisDetails = new ThesisDetails();
        thesisDetails.setOrderIn(1l);
        thesisDetails.setIssue("user");
        thesisDetails.setThesisId(thesisStudy.getId());
        thesisDetails.setContent(content);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);

        thesisDetails.setOrderIn(2l);
        thesisDetails.setIssue("assistant");
        thesisDetails.setThesisId(thesisStudy.getId());

//        String answer = getAnswerLiu(content.replaceAll("\\r\\n|\\r|\\n", ""), conversationId, fileId);
        return getAnswerLiu(content.replaceAll("\\r\\n|\\r|\\n", ""), conversationId, fileId, thesisDetails);


    }

    @Override
    public Flux<String> updateThesisStudyLiu(ThesisStudy thesisStudy) throws IOException, AppBuilderServerException {
        Long id = thesisStudy.getId();
        ThesisStudy thesisStudy1 = thesisStudyMapper.selectThesisStudyById(id);
        String conversationId = thesisStudy1.getConversationId();
        String fileId = thesisStudy1.getFileId();
        String content = thesisStudy.getContent();

        Long maxOrder = thesisDetailsMapper.selectMaxOrderById(id);
        ThesisDetails thesisDetails = new ThesisDetails();
        if (maxOrder != null) {
            thesisDetails.setOrderIn(maxOrder + 1);
        } else {
            thesisDetails.setOrderIn(1l);
        }
        thesisDetails.setId(id);
        thesisDetails.setIssue("user");
        thesisDetails.setThesisId(id);
        thesisDetails.setContent(content);
        thesisDetailsMapper.insertThesisDetails(thesisDetails);

        //增加对话记录明细
        if (maxOrder != null) {
            thesisDetails.setOrderIn(maxOrder + 1);
        } else {
            thesisDetails.setOrderIn(2l);
        }
        thesisDetails.setIssue("assistant");
        thesisDetails.setThesisId(id);

        return getAnswerLiu(content.replaceAll("\\r\\n|\\r|\\n", ""), conversationId, fileId,thesisDetails);

    }

    //新建会话，返回会话id
    public String newSession() {

        String appid = configService.getConfigKey2("appid", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAppid(appid);
        baiduDto.setSecretkey(secretkey);
        String s = baiduApiService.newSession(baiduDto, SecurityConstants.INNER);
        return s;
//        String conversation_id = "";
//        MediaType mediaType = MediaType.parse("application/json");
//        RequestBody body = RequestBody.create(mediaType, "{\"app_id\":\"" + appid + "\"}");
//        Request request = new Request.Builder()
//                .url("https://qianfan.baidubce.com/v2/app/conversation")
//                .method("POST", body)
//                .addHeader("Content-Type", "application/json")
//                .addHeader("X-Appbuilder-Authorization", "Bearer " + secretkey)
//                .build();
//        try {
//            Response response = HTTP_CLIENT.newCall(request).execute();
//            String string = response.body().string();
//            JSONObject jsonObject = new JSONObject(string);
//            return jsonObject.getString("conversation_id");
//            //获取 conversation_id 参数 赋值
//        } catch (IOException | JSONException e) {
//            e.printStackTrace();
//        }
//        return conversation_id;
    }

    //获取文件id
    public String getfileId(String conversationId, File file) throws IOException, JSONException {

        String appid = configService.getConfigKey2("appid", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAppid(appid);
        baiduDto.setSecretkey(secretkey);
        baiduDto.setConversationId(conversationId);
        baiduDto.setFile(file);
        String s = baiduApiService.getfileId(baiduDto, SecurityConstants.INNER);
        return s;

//        MultipartBody.Builder builder = new MultipartBody.Builder();
//        builder.addFormDataPart("app_id", appid);
//        builder.addFormDataPart("conversation_id", conversationId);
//        String TYPE = "application/octet-stream";
//        RequestBody fileBody = RequestBody.create(MediaType.parse(TYPE), file);
//
//        RequestBody requestBody = builder
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("file", file.getName(), fileBody)
//                .build();
//
//        Request request = new Request.Builder()
//                .url("https://qianfan.baidubce.com/v2/app/conversation/file/upload")
//                .post(requestBody)
//                .addHeader("Authorization", "Bearer " + secretkey)
//                .build();
//        Response response = HTTP_CLIENT.newCall(request).execute();
//        String string = response.body().string();
//        JSONObject jsonObject = new JSONObject(string);
//        return jsonObject.getString("id");

    }

    //获取回答
    public String getAnswer(String query, String conversationId, String fileId) {
        String answer = "";
        String appid = configService.getConfigKey2("appid", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAppid(appid);
        baiduDto.setSecretkey(secretkey);
        baiduDto.setConversationId(conversationId);
        baiduDto.setFileId(fileId);
        baiduDto.setQuery(query);
        String answer1 = baiduApiService.getAnswer(baiduDto, SecurityConstants.INNER);
        return answer1;
//        MediaType mediaType = MediaType.parse("application/json");
//        //stream":true//流接受
//        RequestBody body = RequestBody.create(mediaType, "{\"app_id\":\"" + appid + "\",\"query\":\"" + query + "\",\"stream\":false,\"conversation_id\":\"" + conversationId + "\",\"file_ids\":[\"" + fileId + "\"]}");
//        Request request = new Request.Builder()
//                .url("https://qianfan.baidubce.com/v2/app/conversation/runs")
//                .method("POST", body)
//                .addHeader("Content-Type", "application/json")
//                .addHeader("X-Appbuilder-Authorization", "Bearer " + secretkey)
//                .build();
//        OkHttpClient client = new OkHttpClient.Builder()
//                .connectTimeout(200, TimeUnit.SECONDS) // 设置连接超时时间
//                .readTimeout(200, TimeUnit.SECONDS) // 设置读取超时时间
//                .writeTimeout(200, TimeUnit.SECONDS) // 设置写入超时时间
//                .build();
//        try {
//            Response response = client.newCall(request).execute();
//            String string = response.body().string();
//            JSONObject jsonObject = new JSONObject(string);
//            String regex = "\\^\\[.*?\\]\\^";
//            Pattern pattern = Pattern.compile(regex);
//            Matcher matcher = pattern.matcher(jsonObject.getString("answer"));
//            return matcher.replaceAll("");
//        } catch (IOException | JSONException e) {
//            e.printStackTrace();
//        }
//
//        return answer;
    }

    public Flux<String> getAnswerLiu(String query, String conversationId, String fileId, ThesisDetails thesisDetails) throws IOException, AppBuilderServerException {
        String appid = configService.getConfigKey2("appid", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        String[] fileIds = new String[1];
        fileIds[0] = fileId;
        MediaType mediaType = MediaType.parse("application/json");
        //stream":true//流接受
        System.setProperty("APPBUILDER_TOKEN", secretkey);
        AppBuilderClient builder = new AppBuilderClient(appid);


        return Flux.create(sink -> {
            Thread thread = new Thread(() -> {
                String content = "";
                AppBuilderClientIterator itor = null;
                try {
                    itor = builder.run(query, conversationId, fileIds, true);
                } catch (IOException e) {
                    e.printStackTrace();
                } catch (AppBuilderServerException e) {
                    e.printStackTrace();
                }
                while (itor.hasNext()) {
                    AppBuilderClientResult response = itor.next();
                    String answer = response.getAnswer();
                    if (answer != null && !answer.isEmpty()) {
                        sink.next(answer);
                        content += answer;
                    }
                }
                sink.complete();
                thesisDetails.setContent(content);
                thesisDetailsMapper.insertThesisDetails(thesisDetails);

            });
            thread.start();
        });


    }
}
