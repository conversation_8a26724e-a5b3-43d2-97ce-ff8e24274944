package com.ruoyi.intelligent.utils;

import java.util.concurrent.*;

public class CompletableFutureTimeoutUtil {

    // 使用单例线程池管理超时任务
    private static final ScheduledThreadPoolExecutor DELAYER = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setDaemon(true);
            thread.setName("CompletableFutureTimeoutScheduler");
            return thread;
        }
    });

    static {
        DELAYER.setRemoveOnCancelPolicy(true);
    }

    /**
     * 创建超时 Future，用于在指定时间后触发超时异常
     */
    public static <T> CompletableFuture<T> timeoutAfter(long timeout, TimeUnit unit) {
        CompletableFuture<T> result = new CompletableFuture<>();
        DELAYER.schedule(() -> result.completeExceptionally(new TimeoutException()), timeout, unit);
        return result;
    }

    // 静态工具类，禁止实例化
    private CompletableFutureTimeoutUtil() {}
}
