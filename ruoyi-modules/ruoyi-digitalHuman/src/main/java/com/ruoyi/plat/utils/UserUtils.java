package com.ruoyi.plat.utils;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.RemoteUniversityService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.University;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户工具类
 */
@Component
public class UserUtils {
    @Autowired
    private RemoteUserService userService;

    /*@Autowired
    private RemoteUniversityService universityService;*/

    /**
     * 获取NickName
     * @param userName 用户名称
     * @return
     */
    public String getNickName(String userName) {
        R<LoginUser> loginUserR = userService.getUserInfo(userName, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        return sysUser.getNickName();
    }

    /**
     * 获取NickName
     * @param userId 用户id
     * @return
     */
    public String getNickName(Long userId) {
        R<LoginUser> loginUserR = userService.getUserAndRole(userId, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        return sysUser.getNickName();
    }

    /**
     * 获取用户对象
     * @param userId
     * @return
     */
    public SysUser getSysUser(Long userId){
        R<LoginUser> loginUserR = userService.getUserAndRole(userId, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        return sysUser;
    }

    /**
     * 获取角色名称
     * @param userId
     * @return
     */
    public List<String>  getRoleNameList(Long userId){
        R<LoginUser> loginUserR = userService.getUserAndRole(userId, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        List<SysRole> roleList = sysUser.getRoles();
        List<String> roleNames = roleList.stream()
                .map(SysRole::getRoleName) // 假设Role类有一个getRoleName方法
                .collect(Collectors.toList());
        return roleNames;
    }

    /**
     * 获取角色名称
     * @param userName
     * @return
     */
    public List<String>  getRoleNameList(String userName){
        R<LoginUser> loginUserR = userService.getUserInfo(userName, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        List<SysRole> roleList = sysUser.getRoles();
        List<String> roleNames = roleList.stream()
                .map(SysRole::getRoleName) // 假设Role类有一个getRoleName方法
                .collect(Collectors.toList());
        return roleNames;
    }

    /**
     * 获取用户对象
     * @param userId
     * @return
     */
    /*public SysUser getSysUserAndUniversityInfo(Long userId){
        R<LoginUser> loginUserR = userService.getUserAndRole(userId, SecurityConstants.INNER);
        SysUser sysUser = loginUserR.getData().getSysUser();
        University university = new University();
        university.setId(sysUser.getUniversityId());
        university.setCollegeId(sysUser.getCollegeId());
        university.setMajorId(sysUser.getMajorId());
        university.setClassId(sysUser.getClassId());
        AjaxResult university1 = universityService.getInfo(university,SecurityConstants.INNER);
        *//*sysUser.setUniverName(university1.getUniverName());
        sysUser.setCollegeName(university1.getColleName());
        sysUser.setMajorName(university1.getMajorName());
        sysUser.setClassName(university1.getClassName());*//*
        return sysUser;
    }*/

}
