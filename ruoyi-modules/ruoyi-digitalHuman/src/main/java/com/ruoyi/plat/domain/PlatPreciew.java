package com.ruoyi.plat.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatPreciew {
    //文件ID
    private Long fileId;
    /** 形象ID **/
    private Long figureId;

    private String figureName;

    private String remark;
    /** 尺寸 **/
    private String size;

    private List<String> motionNoList;
    /** 演讲稿-文本 **/
    private String beforeAnalysisContent;

    private String presentationId;//课件id
    private Integer pageIndex;//第几页

    private String consonant;//
    private String motion;//
}
