package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户动作绑定对象 plat_user_figure
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@TableName("plat_user_figure")
@Builder
public class PlatUserFigure implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户ID */
    @TableField("user_name")
    @Excel(name = "用户ID")
    private String userName;

    /** 形象ID */
    @TableField("figure_id")
    @Excel(name = "形象ID")
    private Long figureId;

    /** 发音人 */
    @TableField("per")
    @Excel(name = "发音人")
    private String per;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;

    public PlatUserFigure(Long id, String userName, Long figureId, String per, String createBy, Date createTime, String updateBy, Date updateTime, String remark) {
        this.id = id;
        this.userName = userName;
        this.figureId = figureId;
        this.per = per;
        this.createBy = createBy;
        this.createTime = createTime;
        this.updateBy = updateBy;
        this.updateTime = updateTime;
        this.remark = remark;
    }

    public PlatUserFigure() {
    }
}
