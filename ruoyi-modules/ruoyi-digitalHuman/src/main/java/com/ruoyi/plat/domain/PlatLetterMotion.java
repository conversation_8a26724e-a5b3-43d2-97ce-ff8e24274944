package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 拼音字符与口型动作对应关系对象 plat_letter_motion
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@TableName("plat_letter_motion")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlatLetterMotion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 形象ID */
    @TableField("figure_id")
    @Excel(name = "形象ID")
    private Long figureId;

    /** 拼音字符 */
    @TableField("letters")
    @Excel(name = "拼音字符")
    private String letters;

    /** 字符类型 0-韵母 1-声母 */
    @TableField("letters_type")
    @Excel(name = "字符类型 0-韵母 1-声母")
    private Long lettersType;

    /** 动作ID */
    @TableField("motion_id")
    @Excel(name = "动作ID")
    private Long motionId;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;
}
