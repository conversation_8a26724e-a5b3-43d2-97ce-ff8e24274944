package com.ruoyi.plat.utils;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.plat.constant.SymbolConstant;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AvatarDataExtractor {
    // 获取文件夹下所有文件绝对路径
    public static List<String> getSortedFullPaths(String directoryPath) {
        Path directory = Paths.get(directoryPath);

        try {
            // 获取目录下所有文件的路径
            List<String> filePaths = Files.walk(directory)
                    .filter(Files::isRegularFile) // 只处理文件，不处理目录
                    .sorted((path1, path2) -> compareNumericFileNames(path1, path2)) // 按文件名排序
                    .map(Path::toAbsolutePath)
                    .map(Path::toString)
                    .collect(Collectors.toList());
            List<String> returnFilePaths = filePaths.stream().map(item -> {
                return item.replace("\\", "/");
            }).collect(Collectors.toList());
            return returnFilePaths;
        } catch (IOException e) {
            System.err.println("Error reading the directory: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>(); // 返回空列表作为默认情况
        }
    }

    // 获取文件夹下所有文件相对路径
    public static List<String> getSortedRelativePaths(String absolutePath, String relativePath) {
        String directoryPath = absolutePath + relativePath;
        Path directory = Paths.get(directoryPath);

        try {
            // 获取目录下所有文件的路径
            List<String> filePaths = Files.walk(directory)
                    .filter(Files::isRegularFile) // 只处理文件，不处理目录
                    .sorted((path1, path2) -> compareNumericFileNames(path1, path2)) // 按文件名排序
                    .map(Path::toAbsolutePath)
                    .map(Path::toString)
                    .collect(Collectors.toList());
            List<String> returnFilePaths = filePaths.stream().map(item -> {
                return item.replace("\\", "/").replace(absolutePath, "");
            }).collect(Collectors.toList());
            return returnFilePaths;
        } catch (IOException e) {
            System.err.println("Error reading the directory: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>(); // 返回空列表作为默认情况
        }
    }

    private static int compareNumericFileNames(Path path1, Path path2) {
        String fileName1 = StrUtil.subBefore(path1.getFileName().toString(), SymbolConstant.PERIOD, true) ;
        String fileName2 = StrUtil.subBefore(path2.getFileName().toString(), SymbolConstant.PERIOD, true) ;

        try {
            int numericName1 = Integer.parseInt(fileName1);
            int numericName2 = Integer.parseInt(fileName2);
            return Integer.compare(numericName1, numericName2);
        } catch (NumberFormatException e) {
            // 如果文件名不是纯数字，这里可以按字母顺序排序或返回0
            return fileName1.compareTo(fileName2);
        }
    }
}
