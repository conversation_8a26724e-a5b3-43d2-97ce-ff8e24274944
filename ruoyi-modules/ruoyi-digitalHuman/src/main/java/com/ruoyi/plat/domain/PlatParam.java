package com.ruoyi.plat.domain;

import lombok.Data;

import java.util.List;

@Data
public class PlatParam {
    //文件ID
    private Long fileId;
    /** 形象ID **/
    private Long figureId;

    private String figureName;

    private String remark;
    /** 尺寸 **/
    private String size;

    private List<String> motionNoList;
    /** 演讲稿-文本 **/
    private String beforeAnalysisContent;

    private String presentationId;
    private Integer pageIndex;
    //第几页
    private Integer indexPage;

    //第几句
    private Integer indexSentence;

    //第几页第几句文本
    private String txtSentence;

    //第几页第几句动作标签
    private String motion;

    //课件id 或者 讲演稿路径
    private String presentationFrom;
}
