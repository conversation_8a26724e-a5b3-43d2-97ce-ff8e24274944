package com.ruoyi.plat.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 学生知识点掌握对象 s_knowledge_points
 * 
 * <AUTHOR>
 * @date 2024-10-16
 */
public class KnowledgePoints extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 知识点id */
    @Excel(name = "知识点id")
    private Long textbookId;

    /** 是否掌握 */
    @Excel(name = "是否掌握")
    private String isMaster;

    /** 用户 */
    @Excel(name = "用户")
    private String userName;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTextbookId(Long textbookId)
    {
        this.textbookId = textbookId;
    }

    public Long getTextbookId()
    {
        return textbookId;
    }
    public void setIsMaster(String isMaster)
    {
        this.isMaster = isMaster;
    }

    public String getIsMaster()
    {
        return isMaster;
    }
    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserName()
    {
        return userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("textbookId", getTextbookId())
                .append("isMaster", getIsMaster())
                .append("userName", getUserName())
                .toString();
    }
}
