package com.ruoyi.plat.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 学生回答记录对象 s_student_problem_sub
 * 
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
public class StudentProblemSub extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 问题id */
    @Excel(name = "问题id")
    private Long problemId;

    /** 回答学生id */
    @Excel(name = "回答学生id")
    private String studentId;

    /** 回答答案 */
    @Excel(name = "回答答案")
    private String subAnwer;

    /** 是否正确 */
    @Excel(name = "是否正确")
    private Integer isRight;

}
