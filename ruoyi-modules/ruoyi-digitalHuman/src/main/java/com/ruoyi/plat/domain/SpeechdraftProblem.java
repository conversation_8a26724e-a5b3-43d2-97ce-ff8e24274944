package com.ruoyi.plat.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.Map;

/**
 * 讲演稿题目对象 s_speechdraft_problem
 * 
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
public class SpeechdraftProblem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 问题 */
    @Excel(name = "问题")
    private String problem;

    /** 选项 */
    @Excel(name = "选项")
    private String options;

    /** 答案 */
    @Excel(name = "答案")
    private String anwers;

    /** 课件id */
    @Excel(name = "课件id")
    private Long presentationId;

    /** 第几页 */
    @Excel(name = "第几页")
    private Long index;

    /** 第几题 */
    @Excel(name = "第几题")
    private Long problemIndex;
    /** 选项 map*/
    private Map<Character, String> optionsMap;
}
