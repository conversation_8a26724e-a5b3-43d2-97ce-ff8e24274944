package com.ruoyi.plat.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数字人上传文件类专有配置
 *
 * */
@Component
@ConfigurationProperties(prefix = "plat")
public class PlatConfig {

    private Path path;

    public Path getPath() {
        return path;
    }

    public void setPath(Path path) {
        this.path = path;
    }

}
