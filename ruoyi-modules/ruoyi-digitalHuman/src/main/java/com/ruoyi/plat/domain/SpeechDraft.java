package com.ruoyi.plat.domain;

import lombok.Builder;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Builder
public class SpeechDraft {
    private Long speechdraftId;
    private Integer page;
    private String[] txtSentences;
    private TxtPause[] txtPauses;

    public SpeechDraft(Long speechdraftId, Integer page, String[] txtSentences, TxtPause[] txtPauses) {
        this.speechdraftId = speechdraftId;
        this.page = page;
        this.txtSentences = txtSentences;
        this.txtPauses = txtPauses;
    }

    public SpeechDraft() {
    }

    public TxtPause[] getTxtPauses() {
        return txtPauses;
    }

    public void setTxtPauses(TxtPause[] txtPauses) {
        this.txtPauses = txtPauses;
    }

    // Getter 和 Setter 方法
    public Long getSpeechdraftId() {
        return speechdraftId;
    }

    public void setSpeechdraftId(Long speechdraftId) {
        this.speechdraftId = speechdraftId;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public String[] getTxtSentences() {
        return txtSentences;
    }

    public void setTxtSentences(String[] txtSentences) {
        this.txtSentences = txtSentences;
    }

//    @Override
//    public String toString() {
//        return String.format("{speechdraft_id:%d,page:%d,txtSentences:%s}",
//                speechdraftId,
//                page,
//                Arrays.toString(txtSentences));
//    }
    @Override
    public String toString() {
        String sentences = Arrays.stream(txtSentences)
                .map(sentence -> "\"" + sentence + "\"")
                .collect(Collectors.joining(","));
        return String.format("{speechdraft_id:%d,page:%d,txtSentences:[%s]}",
                speechdraftId,
                page,
                sentences);
    }
}

