package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人形象对象 plat_figure
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@TableName("plat_figure")
public class PlatFigure implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键，形象ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 形象名称 */
    @TableField("figure_name")
    @Excel(name = "形象名称")
    private String figureName;

    /** 版本号 */
    @TableField("version")
    @Excel(name = "版本号")
    private String version;

    /** 预览图路径 */
    @TableField("preview_url")
    @Excel(name = "预览图路径")
    private String previewUrl;

    /** 初始形象路径 */
    @TableField("initial_url")
    @Excel(name = "初始形象路径")
    private String initialUrl;

    /** 状态 0-正常 1-停用 */
    @TableField("status")
    @Excel(name = "状态 0-正常 1-停用")
    private Long status;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;

    /**
     * 文件唯一id
     */
    @TableField("file_object_name")
    @Excel(name = "文件唯一id")
    private Long fileObjectName;
}
