package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 动作明细对象 plat_motion_detail
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@TableName("plat_motion_detail")
public class PlatMotionDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键，图片ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 动作ID */
    @TableField("motion_id")
    @Excel(name = "动作ID")
    private Long motionId;

    /** 图片顺序号 */
    @TableField("img_order")
    @Excel(name = "图片顺序号")
    private Long imgOrder;

    /** 图片路径 */
    @TableField("img_url")
    @Excel(name = "图片路径")
    private String imgUrl;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;

    /**
     * 图片转base64后的信息
     */
    @TableField("base64String")
    @Excel(name = "图片转base64后的信息")
    private String base64String;
}
