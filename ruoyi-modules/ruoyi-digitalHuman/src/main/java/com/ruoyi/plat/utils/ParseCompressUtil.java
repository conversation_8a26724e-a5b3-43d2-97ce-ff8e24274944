package com.ruoyi.plat.utils;

import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Base64;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class ParseCompressUtil {
    // 根据路径生成MultipartFile对象
    public static MultipartFile getFileAsMultipartFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            byte[] fileContent = Files.readAllBytes(path);
            String originalFilename = path.getFileName().toString();

            // Create MockMultipartFile object
            MockMultipartFile multipartFile = new MockMultipartFile(
                    originalFilename, // Original file name
                    originalFilename, // Content disposition filename
                    MediaType.APPLICATION_OCTET_STREAM_VALUE, // MIME type
                    fileContent // File content
            );

            return multipartFile;
        } catch (IOException e) {
            System.err.println("Error reading the file: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    // 解压ZIP文件的方法
    public static void unzip(MultipartFile file, String outputDirectory) throws IOException {
        Path outputPath = Paths.get(outputDirectory);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }

        try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                String filePath = outputDirectory + File.separator + entry.getName();

                if (!entry.isDirectory()) {
                    if (isImageFile(filePath)) {
                        // 处理图片文件
                        processImageFile(zis, filePath);
                    } else if (isExcelFile(filePath)) {
                        // 处理Excel文件
                        processExcelFile(zis, filePath);
                    } else {
                        // 处理其他文件
                        processOtherFile(zis, filePath);
                    }
                } else {
                    createDirectory(filePath);
                }
            }
        }
    }

    /**
     * 根据图片路径将图片转换成字节
     * @param filePath
     * @return
     * @throws IOException
     */
    public static String processing(String filePath) throws IOException {
        File file = new File(filePath); // 图片的实际路径
        BufferedImage image = ImageIO.read(file);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, getFileExtension(filePath), baos);
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();

        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 根据图片名称或路径获取图片后缀
     * @param fileName
     * @return
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ""; // 如果文件名为空，返回空字符串
        }
        int dotIndex = fileName.lastIndexOf('.'); // 查找最后一个'.'的位置
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // 如果没有找到'.'或者'.'在字符串末尾，说明没有扩展名
        }
        return fileName.substring(dotIndex + 1); // 返回'.'之后的部分，即文件扩展名
    }

    private static boolean isImageFile(String filePath) {
        return filePath.toLowerCase().endsWith(".jpg") ||
                filePath.toLowerCase().endsWith(".jpeg") ||
                filePath.toLowerCase().endsWith(".png") ||
                filePath.toLowerCase().endsWith(".gif");
    }

    private static boolean isExcelFile(String filePath) {
        return filePath.toLowerCase().endsWith(".xlsx") ||
                filePath.toLowerCase().endsWith(".xls");
    }

    private static void processImageFile(ZipInputStream zis, String filePath) throws IOException {
        try (BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)))) {
            byte[] bytesIn = new byte[4096];
            int read = 0;
            while ((read = zis.read(bytesIn)) != -1) {
                bos.write(bytesIn, 0, read);
            }
        } finally {
            zis.closeEntry(); // 关闭当前的ZipEntry
        }
    }

    private static void processExcelFile(ZipInputStream zis, String filePath) throws IOException {
        try (BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)))) {
            byte[] bytesIn = new byte[4096];
            int read = 0;
            while ((read = zis.read(bytesIn)) != -1) {
                bos.write(bytesIn, 0, read);
            }
        } finally {
            zis.closeEntry(); // 关闭当前的ZipEntry
        }
    }

    private static void processOtherFile(ZipInputStream zis, String filePath) throws IOException {
        try (BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)))) {
            byte[] bytesIn = new byte[4096];
            int read = 0;
            while ((read = zis.read(bytesIn)) != -1) {
                bos.write(bytesIn, 0, read);
            }
        } finally {
            zis.closeEntry(); // 关闭当前的ZipEntry
        }
    }

    private static void createDirectory(String filePath) {
        File directory = new File(filePath);
        directory.mkdirs();
    }

    // 删除指定文件夹文件
    public static void deleteDirectoryWithContent(String directoryPath) {
        Path directory = Paths.get(directoryPath);

        try {
            // 遍历目录中的所有文件和子目录
            Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    // 删除文件
                    Files.delete(file);
                    return super.visitFile(file, attrs);
                }

                @Override
                public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                    // 在所有文件和子目录都被删除后，删除目录本身
                    Files.delete(dir);
                    return super.postVisitDirectory(dir, exc);
                }
            });
        } catch (IOException e) {
            System.err.println("无法删除目录 " + directory + ": " + e.getMessage());
        }
    }
}
