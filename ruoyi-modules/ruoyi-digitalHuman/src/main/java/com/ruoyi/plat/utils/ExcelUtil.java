package com.ruoyi.plat.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelUtil {
    public static Map<String, List<List<String>>> readAndParseExcel(String filePath, int startColumnIndex, int skipFirstRow) {
        Map<String, List<List<String>>> allSheetsData = new HashMap<>();

        try (FileInputStream file = new FileInputStream(new File(filePath))) {
            Workbook workbook = new XSSFWorkbook(file); // 用于 .xlsx 文件
            // 如果是 .xls 文件，则使用 WorkbookFactory.create(file) 或 HSSFWorkbook

            // 遍历每一个工作表
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                List<List<String>> sheetData = new ArrayList<>();

                // 遍历每一行
                int rowIndex = 0;
                for (Row row : sheet) {
                    if (rowIndex < skipFirstRow) {
                        rowIndex++;
                        continue;
                    }
                    List<String> rowData = new ArrayList<>();

                    // 遍历每一列
                    for (int columnIndex = startColumnIndex; columnIndex < row.getLastCellNum(); columnIndex++) {
                        Cell cell = row.getCell(columnIndex);

                        // 获取单元格数据
                        String cellValue = "";
                        if (cell != null) {
                            switch (cell.getCellType()) {
                                case Cell.CELL_TYPE_STRING:
                                    cellValue = cell.getStringCellValue();
                                    break;
                                case Cell.CELL_TYPE_NUMERIC:
                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                    break;
                                case Cell.CELL_TYPE_BOOLEAN:
                                    cellValue = String.valueOf(cell.getBooleanCellValue());
                                    break;
                                default:
                                    cellValue = "";
                            }
                        }
                        rowData.add(cellValue);
                    }
                    sheetData.add(rowData);
                    rowIndex++;
                }
                allSheetsData.put(sheet.getSheetName(), sheetData);
            }

        } catch (IOException e) {
            throw new RuntimeException("Error reading Excel file.", e);
        }

        return allSheetsData;
    }
}
