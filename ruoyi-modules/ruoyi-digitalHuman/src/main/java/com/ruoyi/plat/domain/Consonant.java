package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.Transient;

/**
 * 讲演稿声母韵母对象 s_consonant
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Data
@Builder
public class Consonant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 讲演稿文件 */
    @Excel(name = "讲演稿文件")
    private String speechdraftId;

    /** 声母韵母 示例  声母:韵母/声母:韵母/声母:韵母/声母:韵母 */
    @Excel(name = "声母韵母 示例  声母:韵母/声母:韵母/声母:韵母/声母:韵母")
    private String consonant;

    /** 讲演稿第几页的解析 */
    @Excel(name = "讲演稿第几页的解析")
    private String indexPage;

    /** 讲演稿第几页第几句话的解析 */
    @Excel(name = "")
    private String indexSentence;

    //第几页第几句文本
    private String txtSentence;

    //第几页第几句动作标签
    private String motion;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;


    @TableField(exist = false)
    private boolean isHavePause;

    @TableField(exist = false)
    private Integer timeOut;

    public String getTxtSentence() {
        return txtSentence;
    }

    public Consonant() {
    }

    public Consonant(Long id, String speechdraftId, String consonant, String indexPage, String indexSentence, String txtSentence, String motion, Integer status, boolean isHavePause, Integer timeOut) {
        this.id = id;
        this.speechdraftId = speechdraftId;
        this.consonant = consonant;
        this.indexPage = indexPage;
        this.indexSentence = indexSentence;
        this.txtSentence = txtSentence;
        this.motion = motion;
        this.status = status;
        this.isHavePause = isHavePause;
        this.timeOut = timeOut;
    }

    public void setTxtSentence(String txtSentence) {
        this.txtSentence = txtSentence;
    }

    public String getMotion() {
        return motion;
    }

    public void setMotion(String motion) {
        this.motion = motion;
    }

    public boolean isHavePause() {
        return isHavePause;
    }

    public void setHavePause(boolean havePause) {
        isHavePause = havePause;
    }

    public Integer getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(Integer timeOut) {
        this.timeOut = timeOut;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSpeechdraftId(String speechdraftId)
    {
        this.speechdraftId = speechdraftId;
    }

    public String getSpeechdraftId()
    {
        return speechdraftId;
    }
    public void setConsonant(String consonant)
    {
        this.consonant = consonant;
    }

    public String getConsonant()
    {
        return consonant;
    }
    public void setIndexPage(String indexPage)
    {
        this.indexPage = indexPage;
    }

    public String getIndexPage()
    {
        return indexPage;
    }
    public void setIndexSentence(String indexSentence)
    {
        this.indexSentence = indexSentence;
    }

    public String getIndexSentence()
    {
        return indexSentence;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("speechdraftId", getSpeechdraftId())
            .append("consonant", getConsonant())
            .append("indexPage", getIndexPage())
            .append("indexSentence", getIndexSentence())
            .append("remark", getRemark())
            .append("status", getStatus())
            .append("updateTime", getUpdateTime())
            .append("createTime", getCreateTime())
            .toString();
    }
}
