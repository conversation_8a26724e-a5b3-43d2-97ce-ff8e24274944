package com.ruoyi.plat.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 知识库知识图谱-教材关键词解析对象 s_textbook_keyword_analysis
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
public class TextbookKeywordAnalysis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 教材id */
    private Long textbookId;

    /** 知识点类别 */
    @Excel(name = "知识点类别")
    private String knowledgeCategory;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 关键词 */
    @Excel(name = "关键词")
    private String keyword;

    /** 章节 */
    @Excel(name = "章节")
    private String chapter;

    private Long fileId;

    /** 是否掌握 */
    @Excel(name = "是否掌握")
    private String isMaster;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTextbookId(Long textbookId) 
    {
        this.textbookId = textbookId;
    }

    public Long getTextbookId() 
    {
        return textbookId;
    }
    public void setKnowledgeCategory(String knowledgeCategory) 
    {
        this.knowledgeCategory = knowledgeCategory;
    }

    public String getKnowledgeCategory() 
    {
        return knowledgeCategory;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setKeyword(String keyword) 
    {
        this.keyword = keyword;
    }

    public String getKeyword() 
    {
        return keyword;
    }

    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }


    public String getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(String isMaster) {
        this.isMaster = isMaster;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("textbookId", getTextbookId())
            .append("knowledgeCategory", getKnowledgeCategory())
            .append("category", getCategory())
            .append("keyword", getKeyword())
            .toString();
    }
}
