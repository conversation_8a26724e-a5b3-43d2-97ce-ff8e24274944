package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 动作信息对象 plat_motion
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@TableName("plat_motion")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatMotion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键，动作ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 动作名称 */
    @TableField("motion_name")
    @Excel(name = "动作名称")
    private String motionName;

    /** 形象ID */
    @TableField("figure_id")
    @Excel(name = "形象ID")
    private Long figureId;

    /** 尺寸 */
    @TableField("size")
    @Excel(name = "尺寸")
    private String size;

    /** 动作类型 0-口型 1-手势 */
    @TableField("motion_type")
    @Excel(name = "动作类型 0-口型 1-手势")
    private Long motionType;

    /** 动作编号 motion_type为1时不能为空 */
    @TableField("motion_no")
    @Excel(name = "动作编号 motion_type为1时不能为空")
    private String motionNo;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;

    private String consonant;
}
