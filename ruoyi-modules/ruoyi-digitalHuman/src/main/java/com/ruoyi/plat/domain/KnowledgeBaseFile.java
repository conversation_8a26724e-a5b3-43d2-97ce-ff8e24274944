package com.ruoyi.plat.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 知识库文件对象 s_knowledge_base_file
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@TableName("s_knowledge_base_file")
public class KnowledgeBaseFile
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableField("id")
    private Long id;

    /** 知识库返回的文件id */
    @TableField("file_id")
    private String fileId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    private String fileName;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    @TableField("kb_id")
    private String kbId;

    /** 存储路径 */
    @Excel(name = "存储路径")
    @TableField("file_path")
    private String filePath;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @TableField("created_at")
    private String createdAt;

    /** 索引状态 */
    @Excel(name = "索引状态")
    @TableField("indexing_status")
    private String indexingStatus;

    /** 错误 */
    @Excel(name = "错误")
    @TableField("error")
    private String error;

    /** 是否启用 */
    @Excel(name = "是否启用")
    @TableField("enabled")
    private Integer enabled;

    /** 禁用时间 */
    @Excel(name = "禁用时间")
    @TableField("disabled_at")
    private String disabledAt;

    /** 禁用人 */
    @Excel(name = "禁用人")
    @TableField("disabled_by")
    private String disabledBy;

    /** 显示状态 */
    @Excel(name = "显示状态")
    @TableField("display_status")
    private String displayStatus;

    /** 字节数量 */
    @Excel(name = "字节数量")
    @TableField("word_count")
    private Long wordCount;

    /** 字节数量 */
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /** 字节数量 */
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /** 专业ID */
    @TableField("major_id")
    private Long majorId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    @TableField("course_name")
    private String courseName;

    /** 学科ID */
    @Excel(name = "学科ID")
    @TableField("discipline_id")
    private Long disciplineId;

    /** 解析状态 */
    @Excel(name = "解析状态")
    @TableField("parse_status")
    private String parseStatus;

    /** 提交状态 */
    @Excel(name = "提交状态")
    @TableField("submit_status")
    private String submitStatus;

    @TableField(exist = false)
    private List<KnowledgeBaseFile> children;

    @TableField(exist = false)
    private String flag;
    /** 文件是否能解析 0-否 1-是*/
    @TableField(exist = false)
    private String parseFlag;

    /** 用户是否能对文件操作标志 0-false 1-true*/
    @TableField(exist = false)
    private Boolean operateFlag;

    /** 字节数量 */
    @Excel(name = "用户名称")
    @TableField(exist = false)
    private String nickName;
    /** 是否联盟课 */
    @TableField("is_alliance_course")
    private String isAllianceCourse;

    /** 字节数量 */
    @Excel(name = "审核标志")
    @TableField(exist = false)
    private String examineFlag;

    /** 字节数量 */
    @Excel(name = "审核意见")
    @TableField(exist = false)
    private String examineMessage;

    /** 出版社 */
    @TableField(exist = false)
    private String publishingHouse;

    /** 作者 */
    @TableField(exist = false)
    private String author;

    @TableField(exist = false)
    private List<String> courseList;

    @TableField(exist = false)
    private List<Long> majorList;

    @TableField(exist = false)
    private List<TextbookKeywordAnalysis> textbookKeywordAnalysisList;

}
