package com.ruoyi.zhi.domain;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 上传文件类专有配置
 *
 * */
@Component
@ConfigurationProperties(prefix = "file")
public class FileConfig {

    private Path path;

    private Path export;

    private Express express;

    public Path getExport() {
        return export;
    }

    public void setExport(Path export) {
        this.export = export;
    }

    public Path getPath() {
        return path;
    }

    public void setPath(Path path) {
        this.path = path;
    }

    public Express getExpress() {
        return express;
    }

    public void setExpress(Express express) {
        this.express = express;
    }
}
