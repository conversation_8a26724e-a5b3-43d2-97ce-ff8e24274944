package com.ruoyi.zhi.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 规则内容对象 s_rules_content
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
public class RulesContent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long rulesId;

    /** 标记内容 */
    @Excel(name = "标记内容")
    private String tagContent;

    /** 标记类型 */
    @Excel(name = "标记类型")
    private String tagType;

    /** 内容方位 */
    @Excel(name = "内容方位")
    private String contentPosition;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRulesId(Long rulesId) 
    {
        this.rulesId = rulesId;
    }

    public Long getRulesId() 
    {
        return rulesId;
    }
    public void setTagContent(String tagContent) 
    {
        this.tagContent = tagContent;
    }

    public String getTagContent() 
    {
        return tagContent;
    }
    public void setTagType(String tagType) 
    {
        this.tagType = tagType;
    }

    public String getTagType() 
    {
        return tagType;
    }
    public void setContentPosition(String contentPosition) 
    {
        this.contentPosition = contentPosition;
    }

    public String getContentPosition()
    {
        return contentPosition;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("rulesId", getRulesId())
            .append("tagContent", getTagContent())
            .append("tagType", getTagType())
            .append("contentPosition", getContentPosition())
            .toString();
    }
}
