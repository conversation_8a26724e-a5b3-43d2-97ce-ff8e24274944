package com.ruoyi.zhi.domain;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.system.api.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
//@RefreshScope
public class NacosClient {
    /* 个人帐号 */
//    @Value("${dmx.ak}")
//    public   String ak;
//    @Value("${dmx.sk}")
//    public  String sk;
//    //BOS对象存储-BOS域名
//    @Value("${bos.domainName}")
//    public  String domainName;
//    //BOS对象存储-Bucket名称
//    @Value("${bos.bosBucketName}")
//    public  String bosBucketName;
//
//    /* 企业帐号 */
//    @Value("${dmxqy.ak}")
//    public  String enterpriseAk;
//    @Value("${dmxqy.sk}")
//    public  String enterpriseSk;
//    //BOS对象存储-BOS域名
//    @Value("${bosqy.domainName}")
//    public  String epdomainName;
//    //BOS对象存储-Bucket名称
//    @Value("${bosqy.bosBucketName}")
//    public  String epbosBucketName;
    @Autowired
    private ConfigService configService;
    /* 个人帐号 */
    public String ak;
    public String sk;
    public String apiKey;
    public String apiSK;
    public String apiURL;
    public String domainName;
    public String bosBucketName;
    /* 企业帐号 */
    public String enterpriseAk;
    public String enterpriseSk;
    public String enterpriseApiKey;
    public String enterpriseApiSK;
    public String enterpriseApiURL;
    public String keyword;
    public String epdomainName;
    public String epbosBucketName;
    /*知识库*/
    public String appid;
    public String secretkey;

    public String getAk() {
        ak = configService.getConfigKey2("Personal_service_ak", SecurityConstants.INNER).get("msg").toString();
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        sk = configService.getConfigKey2("Personal_service_sk", SecurityConstants.INNER).get("msg").toString();
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public String getApiKey() {
        apiKey = configService.getConfigKey2("Personal_service_apiKey", SecurityConstants.INNER).get("msg").toString();
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiSK() {
        apiSK = configService.getConfigKey2("Personal_service_secretKey", SecurityConstants.INNER).get("msg").toString();
        return apiSK;
    }

    public void setApiSK(String apiSK) {
        this.apiSK = apiSK;
    }

    public String getApiURL() {
        apiURL = configService.getConfigKey2("Personal_service_address", SecurityConstants.INNER).get("msg").toString();
        return apiURL;
    }

    public void setApiURL(String apiURL) {
        this.apiURL = apiURL;
    }

    public String getDomainName() {
        domainName = configService.getConfigKey2("Personal_bos_domainName", SecurityConstants.INNER).get("msg").toString();
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getBosBucketName() {
        bosBucketName = configService.getConfigKey2("Personal_bos_bosBucketName", SecurityConstants.INNER).get("msg").toString();
        return bosBucketName;
    }

    public void setBosBucketName(String bosBucketName) {
        this.bosBucketName = bosBucketName;
    }

    public String getEnterpriseAk() {
        enterpriseAk = configService.getConfigKey2("enterprise_service_ak", SecurityConstants.INNER).get("msg").toString();
        return enterpriseAk;
    }

    public void setEnterpriseAk(String enterpriseAk) {
        this.enterpriseAk = enterpriseAk;
    }

    public String getEnterpriseSk() {
        enterpriseSk = configService.getConfigKey2("enterprise_service_sk", SecurityConstants.INNER).get("msg").toString();
        return enterpriseSk;
    }

    public void setEnterpriseSk(String enterpriseSk) {
        this.enterpriseSk = enterpriseSk;
    }

    public String getEnterpriseApiKey() {
        enterpriseApiKey = configService.getConfigKey2("enterprise_service_apiKey", SecurityConstants.INNER).get("msg").toString();
        return enterpriseApiKey;
    }

    public void setEnterpriseApiKey(String enterpriseApiKey) {
        this.enterpriseApiKey = enterpriseApiKey;
    }

    public String getEnterpriseApiSK() {
        enterpriseApiSK = configService.getConfigKey2("enterprise_service_secretKey", SecurityConstants.INNER).get("msg").toString();
        return enterpriseApiSK;
    }

    public void setEnterpriseApiSK(String enterpriseApiSK) {
        this.enterpriseApiSK = enterpriseApiSK;
    }

    public String getEnterpriseApiURL() {
        enterpriseApiURL = configService.getConfigKey2("enterprise_service_address", SecurityConstants.INNER).get("msg").toString();
        return enterpriseApiURL;
    }

    public void setEnterpriseApiURL(String enterpriseApiURL) {
        this.enterpriseApiURL = enterpriseApiURL;
    }

    public String getKeyword() {
        keyword = configService.getConfigKey2("enterprise_service_keyword", SecurityConstants.INNER).get("msg").toString();
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getEpdomainName() {
        epdomainName = configService.getConfigKey2("enterprise_bos_domainName", SecurityConstants.INNER).get("msg").toString();
        return epdomainName;
    }

    public void setEpdomainName(String epdomainName) {
        this.epdomainName = epdomainName;
    }

    public String getEpbosBucketName() {
        epbosBucketName = configService.getConfigKey2("enterprise_bos_bosBucketName", SecurityConstants.INNER).get("msg").toString();
        return epbosBucketName;
    }

    public void setEpbosBucketName(String epbosBucketName) {
        this.epbosBucketName = epbosBucketName;
    }

    public String getAppid() {
        appid = configService.getConfigKey2("appid", SecurityConstants.INNER).get("msg").toString();
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getSecretkey() {
        secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        return secretkey;
    }

    public void setSecretkey(String secretkey) {
        this.secretkey = secretkey;
    }
}
