package com.ruoyi.zhi.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 文献整理- 教材原始数据对象 s_textbook_data
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Data
public class TextbookData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 教材id */
    @Excel(name = "教材id")
    @ExcelProperty(value = "ID",index = 0)
    private Long textbookId;

    /** 学科 */
    @Excel(name = "学科")
    @ExcelProperty(value = "学科",index = 1)
    private String academicDiscipline;

    /** 大学名称 */
    private String universityName;

    /** 学院名称 */
    @Excel(name = "院系名称")
    @ExcelProperty(value = "院系",index = 2)
    private String colleName;

    /** 专业级别(本科生/研究生) */
    @Excel(name = "专业级别(本科生/研究生)")
    @ExcelProperty(value = "专业级别(本科生/研究生)",index = 3)
    private String educationalLevel;

    /** 专业名称 */
    @Excel(name = "专业名称")
    @ExcelProperty(value = "专业",index = 4)
    private String majorName;

    /** 教材 */
    @Excel(name = "教材")
    @ExcelProperty(value = "教材",index = 5)
    private String textbook;

    /** 出版社 */
    @Excel(name = "出版社")
    @ExcelProperty(value = "出版社",index = 6)
    private String publishingHouse;

    /** 作者 */
    @Excel(name = "作者")
    @ExcelProperty(value = "作者",index = 7)
    private String author;

    /** 知识点类别 */
    @ExcelProperty(value = "知识点类别",index =8)
    private String knowledgeCategory;

    /** 类别 */
    @Excel(name = "类别")
    @ExcelProperty(value = "类别",index = 9)
    private String category;

    /** 关键词 */
    @Excel(name = "关键词")
    @ExcelProperty(value = "关键词",index = 10)
    private String keyword;

    /** 课程 */
    private String course;

    /** 字段（前端传入的） **/
    private String keywordValue;
    /** 字段 **/
    private String field;
    /** 文件ID **/
    private String fileId;
    /** 统计数量 **/
    private Long count;
    /** 返回前端数据关联关系 */
    List<LiteratrueLinks> literatrueLinks;

    List<LiteratrueNodesData> literatrueNodesData;

    /** 字节数量 */
    @Excel(name = "审核标志")
    private String examineFlag;

    /** 字节数量 */
    @Excel(name = "审核标志")
    private String examineMessage;
}
