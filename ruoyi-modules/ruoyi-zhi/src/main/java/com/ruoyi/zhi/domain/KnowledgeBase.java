package com.ruoyi.zhi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 知识库信息对象 s_knowledge_base
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
@TableName("s_knowledge_base")
public class KnowledgeBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    @TableField("kb_id")
    private String kbId;

    /** 知识库名称 */
    @Excel(name = "知识库名称")
    @TableField("kb_name")
    private String kbName;

    /** 是否使用自定义文档处理策略 */
    @Excel(name = "是否使用自定义文档处理策略")
    @TableField("is_custom_process_rule")
    private Boolean isCustomProcessRule;

    /** 自定义文档处理策略 */
    @Excel(name = "自定义文档处理策略")
    @TableField("custom_process_rule")
    private String customProcessRule;

    /** 是否开启知识增强 */
    @Excel(name = "是否开启知识增强")
    @TableField("is_enhanced")
    private Boolean isEnhanced;

    /** 菜单路由 */
    @Excel(name = "菜单路由")
    @TableField("menu_routing")
    private String menuRouting;

    @TableField(exist = false)
    private Long[] fileIds;

    /** 文件是否能解析 0-否 1-是*/
    @TableField(exist = false)
    private String parseFlag;

    /** 知识库备注 **/
    @TableField(exist = false)
    private String description;

}
