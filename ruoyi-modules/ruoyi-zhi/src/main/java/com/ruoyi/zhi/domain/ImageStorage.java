package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 图片存储对象 s_image_storage
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public class ImageStorage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 数据集id */
    @Excel(name = "数据集id")
    private Long dataId;

    /** 图片名称 */
    @Excel(name = "图片名称")
    private String fileName;

    /** 存储路径 */
    @Excel(name = "存储路径")
    private String filePath;
    /**
     * 图片编号
     */
    private String imageNumber;



    /**
     * 图片信息
     */
    private String processing;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDataId(Long dataId) 
    {
        this.dataId = dataId;
    }

    public Long getDataId() 
    {
        return dataId;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public String getProcessing() {
        return processing;
    }

    public void setProcessing(String processing) {
        this.processing = processing;
    }

    public String getImageNumber() {
        return imageNumber;
    }

    public void setImageNumber(String imageNumber) {
        this.imageNumber = imageNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataId", getDataId())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .toString();
    }
}
