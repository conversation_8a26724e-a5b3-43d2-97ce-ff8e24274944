package com.ruoyi.zhi.vo;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class NodesDatasVo {

    private Long id;
    /**
     * 名称
     **/
    private String name;
    /**
     * 等级（控制颜色）
     **/
    private Long gradeFlag;
    /**
     * 数量
     **/
    private Long count;
    /**
     * 对应keywordValue
     **/
    private String keywordValue;
    /**
     * 对应keywordLabel
     **/
    private String keywordLabel;

    /**
     * 对应表名_字段名
     **/
    private String tableNameFieldName;
    /**
     * 是否能点击 false-否 true -是
     */
    private Boolean onclickFlag;
    /**
     * 是否为父节点 0-否   1-是
     */
    private String parentFlag;
}
