package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.List;

/**
 * 数据集对象 s_data_set
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class DataSet extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 数据集id */
    @Excel(name = "数据集id")
    private String groupPk;

    /** 数据集版本id */
    @Excel(name = "数据集版本id")
    private String datasetId;

    /** 数据集名称 */
    @Excel(name = "数据集名称")
    private String groupName;

    /** 展示名称 */
    @Excel(name = "展示名称")
    private String displayName;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long versionId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 数据类型  4-文本,7-跨模态 */
    @Excel(name = "数据类型  4-文本,7-跨模态")
    private Long dataType;

    /** 标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片 */
    @Excel(name = "标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片")
    private Long projectType;

    /** 标注模版 2000-Prompt+Response,2001-Prompt+多Response排序,40100-纯文本,40200-Prompt集,70500-Prompt集+图片 */
    @Excel(name = "标注模版 2000-Prompt+Response,2001-Prompt+多Response排序,40100-纯文本,40200-Prompt集,70500-Prompt集+图片")
    private Long templateType;

    /** bucketID */
    @Excel(name = "bucketID")
    private String storageId;

    /** 完整存储路径 */
    @Excel(name = "完整存储路径")
    private String storagePath;

    /** bucket名称 */
    @Excel(name = "bucket名称")
    private String storageName;

    /** bucket下文件夹名称 */
    @Excel(name = "bucket下文件夹名称")
    private String rawStoragePath;

    /** 存储所在区域 */
    @Excel(name = "存储所在区域")
    private String region;

    /** 导入状态  -1-未发起导入,0-待导入,1-导入中,2-导入完成,3-导入失败,4-导入中止 */
    @Excel(name = "导入状态  -1-未发起导入,0-待导入,1-导入中,2-导入完成,3-导入失败,4-导入中止")
    private Long importProgress;

    /** 发布状态  0：未发布,1-发布中,2-发布成功,3-发布失败 */
    @Excel(name = "发布状态  0：未发布,1-发布中,2-发布成功,3-发布失败")
    private Long releaseStatus;

    /** 清洗状态 0-无状态,1-运行中,2-已完成,3-任务终止,4-任务失败,5-任务暂停 */
    @Excel(name = "清洗状态 0-无状态,1-运行中,2-已完成,3-任务终止,4-任务失败,5-任务暂停")
    private Long etlStatus;

    /** 实体数量 */
    @Excel(name = "实体数量")
    private Long entityCount;

    /** 已标注的实体数 */
    @Excel(name = "已标注的实体数")
    private Long annotatedEntityCount;

    /** 标签数量 */
    @Excel(name = "标签数量")
    private Long labelCount;

    /** 字符数 */
    @Excel(name = "字符数")
    private Long characterCount;

    /**
     * 菜单路由
     */
    public String menuRouting;

    @TableField(exist = false)
    private Long[] fileIds;

    private List<DataInformation> dataInformationList;

    /* 是否是课程负责人 */
    private String isPersonCharge;

    /* 课程名称 */
    private String courseName;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setGroupPk(String groupPk) 
    {
        this.groupPk = groupPk;
    }

    public String getGroupPk() 
    {
        return groupPk;
    }
    public void setDatasetId(String datasetId) 
    {
        this.datasetId = datasetId;
    }

    public String getDatasetId() 
    {
        return datasetId;
    }
    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }
    public void setDisplayName(String displayName) 
    {
        this.displayName = displayName;
    }

    public String getDisplayName() 
    {
        return displayName;
    }
    public void setVersionId(Long versionId) 
    {
        this.versionId = versionId;
    }

    public Long getVersionId() 
    {
        return versionId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDataType(Long dataType) 
    {
        this.dataType = dataType;
    }

    public Long getDataType() 
    {
        return dataType;
    }
    public void setProjectType(Long projectType) 
    {
        this.projectType = projectType;
    }

    public Long getProjectType() 
    {
        return projectType;
    }
    public void setTemplateType(Long templateType) 
    {
        this.templateType = templateType;
    }

    public Long getTemplateType() 
    {
        return templateType;
    }
    public void setStorageId(String storageId) 
    {
        this.storageId = storageId;
    }

    public String getStorageId() 
    {
        return storageId;
    }
    public void setStoragePath(String storagePath) 
    {
        this.storagePath = storagePath;
    }

    public String getStoragePath() 
    {
        return storagePath;
    }
    public void setStorageName(String storageName) 
    {
        this.storageName = storageName;
    }

    public String getStorageName() 
    {
        return storageName;
    }
    public void setRawStoragePath(String rawStoragePath) 
    {
        this.rawStoragePath = rawStoragePath;
    }

    public String getRawStoragePath() 
    {
        return rawStoragePath;
    }
    public void setRegion(String region) 
    {
        this.region = region;
    }

    public String getRegion() 
    {
        return region;
    }
    public void setImportProgress(Long importProgress) 
    {
        this.importProgress = importProgress;
    }

    public Long getImportProgress() 
    {
        return importProgress;
    }
    public void setReleaseStatus(Long releaseStatus) 
    {
        this.releaseStatus = releaseStatus;
    }

    public Long getReleaseStatus() 
    {
        return releaseStatus;
    }
    public void setEtlStatus(Long etlStatus) 
    {
        this.etlStatus = etlStatus;
    }

    public Long getEtlStatus() 
    {
        return etlStatus;
    }
    public void setEntityCount(Long entityCount) 
    {
        this.entityCount = entityCount;
    }

    public Long getEntityCount() 
    {
        return entityCount;
    }
    public void setAnnotatedEntityCount(Long annotatedEntityCount) 
    {
        this.annotatedEntityCount = annotatedEntityCount;
    }

    public Long getAnnotatedEntityCount() 
    {
        return annotatedEntityCount;
    }
    public void setLabelCount(Long labelCount) 
    {
        this.labelCount = labelCount;
    }

    public Long getLabelCount() 
    {
        return labelCount;
    }
    public void setCharacterCount(Long characterCount) 
    {
        this.characterCount = characterCount;
    }

    public Long getCharacterCount() 
    {
        return characterCount;
    }

    public Long[] getFileIds() {
        return fileIds;
    }

    public void setFileIds(Long[] fileIds) {
        this.fileIds = fileIds;
    }

    public List<DataInformation> getDataInformationList() {
        return dataInformationList;
    }

    public void setDataInformationList(List<DataInformation> dataInformationList) {
        this.dataInformationList = dataInformationList;
    }

    public String getMenuRouting() {
        return menuRouting;
    }

    public void setMenuRouting(String menuRouting) {
        this.menuRouting = menuRouting;
    }

    public String getIsPersonCharge() {
        return isPersonCharge;
    }

    public void setIsPersonCharge(String isPersonCharge) {
        this.isPersonCharge = isPersonCharge;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupPk", getGroupPk())
            .append("datasetId", getDatasetId())
            .append("groupName", getGroupName())
            .append("displayName", getDisplayName())
            .append("versionId", getVersionId())
            .append("userId", getUserId())
            .append("dataType", getDataType())
            .append("projectType", getProjectType())
            .append("templateType", getTemplateType())
            .append("storageId", getStorageId())
            .append("storagePath", getStoragePath())
            .append("storageName", getStorageName())
            .append("rawStoragePath", getRawStoragePath())
            .append("region", getRegion())
            .append("importProgress", getImportProgress())
            .append("releaseStatus", getReleaseStatus())
            .append("etlStatus", getEtlStatus())
            .append("entityCount", getEntityCount())
            .append("annotatedEntityCount", getAnnotatedEntityCount())
            .append("labelCount", getLabelCount())
            .append("characterCount", getCharacterCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("fileIds", getFileIds())
            .toString();
    }
}
