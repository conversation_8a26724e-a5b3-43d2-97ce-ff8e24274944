package com.ruoyi.zhi.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 清洗任务对象 s_cleaning_task
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
public class CleaningTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 任务序号 */
    @Excel(name = "任务序号")
    private String etlTaskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String etlTaskName;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 清洗前的源数据集版本ID */
    @Excel(name = "清洗前的源数据集版本ID")
    private Long sourceDatasetStrId;

    /** 清洗前的源数据集版本名称 */
    @Excel(name = "清洗前的源数据集版本名称")
    private String sourceDatasetStrName;

    /** 清洗后的目标数据集版本ID */
    @Excel(name = "清洗后的目标数据集版本ID")
    private Long destDatasetStrId;

    /** 清洗后的目标数据集版本名称 */
    @Excel(name = "清洗后的目标数据集版本名称")
    private String destDatasetStrName;

    /** 数据清洗任务ID */
    @Excel(name = "数据清洗任务ID")
    private Long taskId;

    /** 实体类型 1-图片,2-文本,3-音频,4-视频 */
    @Excel(name = "实体类型 1-图片,2-文本,3-音频,4-视频")
    private Long entityType;

    /** 移除不可见字符 */
    @Excel(name = "移除不可见字符")
    private Boolean removeInvisibleCharacter;

    /** 规范化空格 */
    @Excel(name = "规范化空格")
    private Boolean replaceUniformWhitespace;

    /** 去除乱码 */
    @Excel(name = "去除乱码")
    private Boolean removeNonMeaningCharacters;

    /** 繁体转简体 */
    @Excel(name = "繁体转简体")
    private Boolean replaceTraditionalChineseToSimplified;

    /** 去除网页标识符 */
    @Excel(name = "去除网页标识符")
    private Boolean removeWebIdentifiers;

    /** 去除表情 */
    @Excel(name = "去除表情")
    private Boolean removeEmoji;

    /** 检查文档的词数目 */
    @Excel(name = "检查文档的词数目")
    private Boolean numberWords;

    /** 最小词数目 */
    @Excel(name = "最小词数目")
    private Long numberMinRange;

    /** 最大词数目 */
    @Excel(name = "最大词数目")
    private Long numberMaxRange;

    /** 检查文档的字重复率 */
    @Excel(name = "检查文档的字重复率")
    private Boolean characterRepetitionRemoval;

    /** 字重复率 */
    @Excel(name = "字重复率")
    private String characterRepetitionNumber;

    /** 检查文档的词重复率 */
    @Excel(name = "检查文档的词重复率")
    private String wordRepetitionRemoval;

    /** 词重复率 */
    @Excel(name = "词重复率")
    private String wordRepetitionNumber;

    /** 检查文档的特殊字符率 */
    @Excel(name = "检查文档的特殊字符率")
    private Boolean specialCharacters;

    /** 特殊字符率 */
    @Excel(name = "特殊字符率")
    private String specialNumber;

    /** 检查文档的色情暴力词率 */
    @Excel(name = "检查文档的色情暴力词率")
    private Boolean flaggedWords;

    /** 色情暴力词率 */
    @Excel(name = "色情暴力词率")
    private String flaggedNumber;

    /** 检查文档的语言概率 */
    @Excel(name = "检查文档的语言概率")
    private Boolean langId;

    /** 语言概率 */
    @Excel(name = "语言概率")
    private String langNumber;

    /** 检查文档的困惑度 */
    @Excel(name = "检查文档的困惑度")
    private Boolean perplexity;

    /** 检查文档的困惑度 */
    @Excel(name = "检查文档的困惑度")
    private String perplexityNumber;

    /** 去重配置 */
    @Excel(name = "去重配置")
    private Boolean simhashOperator;

    /** 去除Email */
    @Excel(name = "去除Email")
    private Boolean replaceEmails;

    /** 去除IP地址 */
    @Excel(name = "去除IP地址")
    private Boolean replaceIp;

    /** 去除数字 */
    @Excel(name = "去除数字")
    private Boolean replaceIdentifier;

    /** 去重配置数值 */
    @Excel(name = "去重配置数值")
    private String simhashOperatorNumber;

    /** 清洗状态信息 0-无状态,1-运行中,2-已完成,3-任务终止,4-任务失败,5-任务暂停 */
    @Excel(name = "清洗状态信息 0-无状态,1-运行中,2-已完成,3-任务终止,4-任务失败,5-任务暂停")
    private Long processStatus;

    /** 清洗任务状态,0-正常,1-删除 */
    @Excel(name = "清洗任务状态,0-正常,1-删除")
    private Long status;

    /** 完成时间 */
    @Excel(name = "完成时间")
    private String finishTime;

    /** 创建者名称 */
    @Excel(name = "创建者名称")
    private String creatorName;

    /** 源数据集名称 */
    @Excel(name = "源数据集名称")
    private String sourceDatasetName;

    /** 目标数据集名称 */
    @Excel(name = "目标数据集名称")
    private String destDatasetName;

    /** 清洗结果 */
    @Excel(name = "清洗结果")
    private String etlResult;

    /** 清洗后剩余的实体数量 */
    @Excel(name = "清洗后剩余的实体数量")
    private Long remainingEntity;

    /** 异常原因 */
    @Excel(name = "异常原因")
    private String exceptionResult;

    /** 任务启动时间 */
    @Excel(name = "任务启动时间")
    private String startTime;

    /** 任务结束时间 */
    @Excel(name = "任务结束时间")
    private String endTime;

    /** 更改时间 */
    @Excel(name = "更改时间")
    private String modifyTime;

    /** 清洗日志文件路径 */
    @Excel(name = "清洗日志文件路径")
    private String logPath;

    /** 标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片 */
   // @Excel(name = "标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片")
    private Long projectType;

    /**
     * 菜单路由
     */
    public String menuRouting;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEtlTaskId() {
        return etlTaskId;
    }

    public void setEtlTaskId(String etlTaskId) {
        this.etlTaskId = etlTaskId;
    }

    public String getEtlTaskName() {
        return etlTaskName;
    }

    public void setEtlTaskName(String etlTaskName) {
        this.etlTaskName = etlTaskName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSourceDatasetStrId() {
        return sourceDatasetStrId;
    }

    public void setSourceDatasetStrId(Long sourceDatasetStrId) {
        this.sourceDatasetStrId = sourceDatasetStrId;
    }

    public String getSourceDatasetStrName() {
        return sourceDatasetStrName;
    }

    public void setSourceDatasetStrName(String sourceDatasetStrName) {
        this.sourceDatasetStrName = sourceDatasetStrName;
    }

    public Long getDestDatasetStrId() {
        return destDatasetStrId;
    }

    public void setDestDatasetStrId(Long destDatasetStrId) {
        this.destDatasetStrId = destDatasetStrId;
    }

    public String getDestDatasetStrName() {
        return destDatasetStrName;
    }

    public void setDestDatasetStrName(String destDatasetStrName) {
        this.destDatasetStrName = destDatasetStrName;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getEntityType() {
        return entityType;
    }

    public void setEntityType(Long entityType) {
        this.entityType = entityType;
    }

    public Boolean getRemoveInvisibleCharacter() {
        return removeInvisibleCharacter;
    }

    public void setRemoveInvisibleCharacter(Boolean removeInvisibleCharacter) {
        this.removeInvisibleCharacter = removeInvisibleCharacter;
    }

    public Boolean getReplaceUniformWhitespace() {
        return replaceUniformWhitespace;
    }

    public void setReplaceUniformWhitespace(Boolean replaceUniformWhitespace) {
        this.replaceUniformWhitespace = replaceUniformWhitespace;
    }

    public Boolean getRemoveNonMeaningCharacters() {
        return removeNonMeaningCharacters;
    }

    public void setRemoveNonMeaningCharacters(Boolean removeNonMeaningCharacters) {
        this.removeNonMeaningCharacters = removeNonMeaningCharacters;
    }

    public Boolean getReplaceTraditionalChineseToSimplified() {
        return replaceTraditionalChineseToSimplified;
    }

    public void setReplaceTraditionalChineseToSimplified(Boolean replaceTraditionalChineseToSimplified) {
        this.replaceTraditionalChineseToSimplified = replaceTraditionalChineseToSimplified;
    }

    public Boolean getRemoveWebIdentifiers() {
        return removeWebIdentifiers;
    }

    public void setRemoveWebIdentifiers(Boolean removeWebIdentifiers) {
        this.removeWebIdentifiers = removeWebIdentifiers;
    }

    public Boolean getRemoveEmoji() {
        return removeEmoji;
    }

    public void setRemoveEmoji(Boolean removeEmoji) {
        this.removeEmoji = removeEmoji;
    }

    public Boolean getNumberWords() {
        return numberWords;
    }

    public void setNumberWords(Boolean numberWords) {
        this.numberWords = numberWords;
    }

    public Long getNumberMinRange() {
        return numberMinRange;
    }

    public void setNumberMinRange(Long numberMinRange) {
        this.numberMinRange = numberMinRange;
    }

    public Long getNumberMaxRange() {
        return numberMaxRange;
    }

    public void setNumberMaxRange(Long numberMaxRange) {
        this.numberMaxRange = numberMaxRange;
    }

    public Boolean getCharacterRepetitionRemoval() {
        return characterRepetitionRemoval;
    }

    public void setCharacterRepetitionRemoval(Boolean characterRepetitionRemoval) {
        this.characterRepetitionRemoval = characterRepetitionRemoval;
    }

    public String getCharacterRepetitionNumber() {
        return characterRepetitionNumber;
    }

    public void setCharacterRepetitionNumber(String characterRepetitionNumber) {
        this.characterRepetitionNumber = characterRepetitionNumber;
    }

    public String getWordRepetitionRemoval() {
        return wordRepetitionRemoval;
    }

    public void setWordRepetitionRemoval(String wordRepetitionRemoval) {
        this.wordRepetitionRemoval = wordRepetitionRemoval;
    }

    public String getWordRepetitionNumber() {
        return wordRepetitionNumber;
    }

    public void setWordRepetitionNumber(String wordRepetitionNumber) {
        this.wordRepetitionNumber = wordRepetitionNumber;
    }

    public Boolean getSpecialCharacters() {
        return specialCharacters;
    }

    public void setSpecialCharacters(Boolean specialCharacters) {
        this.specialCharacters = specialCharacters;
    }

    public String getSpecialNumber() {
        return specialNumber;
    }

    public void setSpecialNumber(String specialNumber) {
        this.specialNumber = specialNumber;
    }

    public Boolean getFlaggedWords() {
        return flaggedWords;
    }

    public void setFlaggedWords(Boolean flaggedWords) {
        this.flaggedWords = flaggedWords;
    }

    public String getFlaggedNumber() {
        return flaggedNumber;
    }

    public void setFlaggedNumber(String flaggedNumber) {
        this.flaggedNumber = flaggedNumber;
    }

    public Boolean getLangId() {
        return langId;
    }

    public void setLangId(Boolean langId) {
        this.langId = langId;
    }

    public String getLangNumber() {
        return langNumber;
    }

    public void setLangNumber(String langNumber) {
        this.langNumber = langNumber;
    }

    public Boolean getPerplexity() {
        return perplexity;
    }

    public void setPerplexity(Boolean perplexity) {
        this.perplexity = perplexity;
    }

    public String getPerplexityNumber() {
        return perplexityNumber;
    }

    public void setPerplexityNumber(String perplexityNumber) {
        this.perplexityNumber = perplexityNumber;
    }

    public Boolean getSimhashOperator() {
        return simhashOperator;
    }

    public void setSimhashOperator(Boolean simhashOperator) {
        this.simhashOperator = simhashOperator;
    }

    public Boolean getReplaceEmails() {
        return replaceEmails;
    }

    public void setReplaceEmails(Boolean replaceEmails) {
        this.replaceEmails = replaceEmails;
    }

    public Boolean getReplaceIp() {
        return replaceIp;
    }

    public void setReplaceIp(Boolean replaceIp) {
        this.replaceIp = replaceIp;
    }

    public Boolean getReplaceIdentifier() {
        return replaceIdentifier;
    }

    public void setReplaceIdentifier(Boolean replaceIdentifier) {
        this.replaceIdentifier = replaceIdentifier;
    }

    public String getSimhashOperatorNumber() {
        return simhashOperatorNumber;
    }

    public void setSimhashOperatorNumber(String simhashOperatorNumber) {
        this.simhashOperatorNumber = simhashOperatorNumber;
    }

    public Long getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Long processStatus) {
        this.processStatus = processStatus;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getSourceDatasetName() {
        return sourceDatasetName;
    }

    public void setSourceDatasetName(String sourceDatasetName) {
        this.sourceDatasetName = sourceDatasetName;
    }

    public String getDestDatasetName() {
        return destDatasetName;
    }

    public void setDestDatasetName(String destDatasetName) {
        this.destDatasetName = destDatasetName;
    }

    public String getEtlResult() {
        return etlResult;
    }

    public void setEtlResult(String etlResult) {
        this.etlResult = etlResult;
    }

    public Long getRemainingEntity() {
        return remainingEntity;
    }

    public void setRemainingEntity(Long remainingEntity) {
        this.remainingEntity = remainingEntity;
    }

    public String getExceptionResult() {
        return exceptionResult;
    }

    public void setExceptionResult(String exceptionResult) {
        this.exceptionResult = exceptionResult;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public Long getProjectType() {
        return projectType;
    }

    public void setProjectType(Long projectType) {
        this.projectType = projectType;
    }

    public String getMenuRouting() {
        return menuRouting;
    }

    public void setMenuRouting(String menuRouting) {
        this.menuRouting = menuRouting;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("etlTaskId", getEtlTaskId())
                .append("etlTaskName", getEtlTaskName())
                .append("userId", getUserId())
                .append("sourceDatasetStrId", getSourceDatasetStrId())
                .append("sourceDatasetStrName", getSourceDatasetStrName())
                .append("destDatasetStrId", getDestDatasetStrId())
                .append("destDatasetStrName", getDestDatasetStrName())
                .append("taskId", getTaskId())
                .append("entityType", getEntityType())
                .append("removeInvisibleCharacter", getRemoveInvisibleCharacter())
                .append("replaceUniformWhitespace", getReplaceUniformWhitespace())
                .append("removeNonMeaningCharacters", getRemoveNonMeaningCharacters())
                .append("replaceTraditionalChineseToSimplified", getReplaceTraditionalChineseToSimplified())
                .append("removeWebIdentifiers", getRemoveWebIdentifiers())
                .append("removeEmoji", getRemoveEmoji())
                .append("numberWords", getNumberWords())
                .append("numberMinRange", getNumberMinRange())
                .append("numberMaxRange", getNumberMaxRange())
                .append("characterRepetitionRemoval", getCharacterRepetitionRemoval())
                .append("characterRepetitionNumber", getCharacterRepetitionNumber())
                .append("wordRepetitionRemoval", getWordRepetitionRemoval())
                .append("wordRepetitionNumber", getWordRepetitionNumber())
                .append("specialCharacters", getSpecialCharacters())
                .append("specialNumber", getSpecialNumber())
                .append("flaggedWords", getFlaggedWords())
                .append("flaggedNumber", getFlaggedNumber())
                .append("langId", getLangId())
                .append("langNumber", getLangNumber())
                .append("perplexity", getPerplexity())
                .append("perplexityNumber", getPerplexityNumber())
                .append("simhashOperator", getSimhashOperator())
                .append("replaceEmails", getReplaceEmails())
                .append("replaceIp", getReplaceIp())
                .append("replaceIdentifier", getReplaceIdentifier())
                .append("simhashOperatorNumber", getSimhashOperatorNumber())
                .append("processStatus", getProcessStatus())
                .append("status", getStatus())
                .append("finishTime", getFinishTime())
                .append("creatorName", getCreatorName())
                .append("sourceDatasetName", getSourceDatasetName())
                .append("destDatasetName", getDestDatasetName())
                .append("etlResult", getEtlResult())
                .append("remainingEntity", getRemainingEntity())
                .append("exceptionResult", getExceptionResult())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("modifyTime", getModifyTime())
                .append("logPath", getLogPath())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("projectType", getProjectType())
                .toString();
    }
}
