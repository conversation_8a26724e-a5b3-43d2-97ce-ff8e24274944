package com.ruoyi.zhi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 数据集制作任务对象 s_poduction_task
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public class PoductionTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 总条数 */
    @Excel(name = "总条数")
    private Long totalNumber;

    /** 任务状态 */
    @Excel(name = "任务状态")
    private String taskStatus;

    /** 菜单路由 */
    @Excel(name = "菜单路由")
    private String menuRouting;

    @TableField(exist = false)
    private Long[] fileIds;



    /**
     *
     */
    private String DataSetName;


    public String getDataSetName() {
        return DataSetName;
    }

    public void setDataSetName(String dataSetName) {
        DataSetName = dataSetName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setFileName(String fileName)
    {
        this.fileName = fileName;
    }

    public String getFileName()
    {
        return fileName;
    }
    public void setTotalNumber(Long totalNumber)
    {
        this.totalNumber = totalNumber;
    }

    public Long getTotalNumber()
    {
        return totalNumber;
    }
    public void setTaskStatus(String taskStatus)
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus()
    {
        return taskStatus;
    }
    public void setMenuRouting(String menuRouting)
    {
        this.menuRouting = menuRouting;
    }

    public String getMenuRouting()
    {
        return menuRouting;
    }

    public Long[] getFileIds() {
        return fileIds;
    }

    public void setFileIds(Long[] fileIds) {
        this.fileIds = fileIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("fileName", getFileName())
                .append("totalNumber", getTotalNumber())
                .append("taskStatus", getTaskStatus())
                .append("menuRouting", getMenuRouting())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
