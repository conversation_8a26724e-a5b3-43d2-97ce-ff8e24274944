package com.ruoyi.zhi.utils;

public class SubString {
    public static String subString(String menuRouting) {

        // 使用lastIndexOf找到最后一个'/'的位置
        int lastSlashIndex = menuRouting.lastIndexOf("/");

        // 如果找到了'/'
        if (lastSlashIndex != -1) {
            // 提取从开始到'/'及其前面的内容，+1是为了包含'/'本身
            return menuRouting.substring(0, lastSlashIndex + 1);
        } else {
            // 如果没有找到'/'，返回空字符串或根据需求处理
            return "";
        }
    }
}
