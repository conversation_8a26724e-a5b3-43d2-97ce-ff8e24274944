package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 知识库章节解析对象 s_chapter_parse
 * 
 * <AUTHOR>
 * @date 2024-10-14
 */
public class ChapterParse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 教材id */
    @Excel(name = "教材id")
    private Long textbookId;

    /** 章节 */
    @Excel(name = "章节")
    private String chapter;

    /** 解析状态 */
    @Excel(name = "解析状态")
    private String parseStatus;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTextbookId(Long textbookId) 
    {
        this.textbookId = textbookId;
    }

    public Long getTextbookId() 
    {
        return textbookId;
    }
    public void setChapter(String chapter) 
    {
        this.chapter = chapter;
    }

    public String getChapter() 
    {
        return chapter;
    }
    public void setParseStatus(String parseStatus) 
    {
        this.parseStatus = parseStatus;
    }

    public String getParseStatus() 
    {
        return parseStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("textbookId", getTextbookId())
            .append("chapter", getChapter())
            .append("parseStatus", getParseStatus())
            .toString();
    }
}
