package com.ruoyi.zhi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 联盟课程对象 s_union_courses
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("s_union_courses")
@ApiModel(description = "联盟课程对象domain")
public class UnionCourses extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 联盟名称
     */
    @Excel(name = "联盟名称")
    @ApiModelProperty("联盟名称")
    private String name;

    /**
     * 联盟学校ids
     */
    @Excel(name = "联盟学校ids")
    @ApiModelProperty("联盟学校ids")
    private String schoolIds;

    @TableField(exist = false)
    private String[] schoolIdAray;

    /**
     * 课程名字
     */
    @Excel(name = "课程名字")
    @ApiModelProperty("课程名字")
    private String courseName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("schoolIds", getSchoolIds())
                .append("courseName", getCourseName())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }

    @ApiModelProperty("登录人id")
    @TableField(exist = false)
    private Long loginUserId;

    @ApiModelProperty("登录人学校id")
    @TableField(exist = false)
    private Long loginUserUniversityId;
}
