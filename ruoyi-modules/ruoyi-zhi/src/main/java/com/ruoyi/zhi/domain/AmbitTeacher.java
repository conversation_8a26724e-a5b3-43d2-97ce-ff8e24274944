package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 专业教师对象 s_ambit_teacher
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
public class AmbitTeacher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 专业ID */
    @Excel(name = "专业ID")
    private Long majorId;

    private Long teacherId;

    /** 学科ID */
    @Excel(name = "学科ID")
    private Long disciplineId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别（0男 1女 2未知） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 职称 */
    @Excel(name = "职称")
    private String title;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 研究方向 */
    @Excel(name = "研究方向")
    private String researchDirection;

    /**课程名称*/
    private String courseName;

    /**课题组类型*/
    private String researchType;

    /**研究成果*/
    private List<TeacherFindings> teacherFindingsList;

    /** 学校id */
    private Long univerId;

    /** 学校名称 */
    private String univerName;
    private String majorName;

    /** 学院id */
    private Long colleId;

    /** 学院名称 */
    private String colleName;

    private Long collegeId;
    private String collegeName;


    private Long[] affiliatedUnit;

    private String isAllianceCourse;

    private String isPersonCharge;

    private String isOneself;

    private String coursesNumber;
    private String textbooksNumber;
    private String knowledgeNumber;

    private String keyword;

    /** 字段（前端传入的） **/
    private String keywordValue;

    /** 返回前端数据关联关系 */
    List<LiteratrueLinks> literatrueLinks;

    List<LiteratrueNodesData> literatrueNodesData;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMajorId(Long majorId) 
    {
        this.majorId = majorId;
    }

    public Long getMajorId() 
    {
        return majorId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setResearchDirection(String researchDirection) 
    {
        this.researchDirection = researchDirection;
    }

    public String getResearchDirection() 
    {
        return researchDirection;
    }

    public List<TeacherFindings> getTeacherFindingsList() {
        return teacherFindingsList;
    }

    public void setTeacherFindingsList(List<TeacherFindings> teacherFindingsList) {
        this.teacherFindingsList = teacherFindingsList;
    }

    public Long getDisciplineId() {
        return disciplineId;
    }

    public void setDisciplineId(Long disciplineId) {
        this.disciplineId = disciplineId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getResearchType() {
        return researchType;
    }

    public void setResearchType(String researchType) {
        this.researchType = researchType;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getUniverId() {
        return univerId;
    }

    public void setUniverId(Long univerId) {
        this.univerId = univerId;
    }

    public String getUniverName() {
        return univerName;
    }

    public void setUniverName(String univerName) {
        this.univerName = univerName;
    }

    public Long getColleId() {
        return colleId;
    }

    public void setColleId(Long colleId) {
        this.colleId = colleId;
    }

    public String getColleName() {
        return colleName;
    }

    public void setColleName(String colleName) {
        this.colleName = colleName;
    }

    public String getIsPersonCharge() {
        return isPersonCharge;
    }

    public void setIsPersonCharge(String isPersonCharge) {
        this.isPersonCharge = isPersonCharge;
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public Long getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Long collegeId) {
        this.collegeId = collegeId;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getIsAllianceCourse() {
        return isAllianceCourse;
    }

    public String getIsOneself() {
        return isOneself;
    }

    public void setIsOneself(String isOneself) {
        this.isOneself = isOneself;
    }

    public Long[] getAffiliatedUnit() {
        return affiliatedUnit;
    }

    public void setAffiliatedUnit(Long[] affiliatedUnit) {
        this.affiliatedUnit = affiliatedUnit;
    }

    public void setIsAllianceCourse(String isAllianceCourse) {
        this.isAllianceCourse = isAllianceCourse;
    }

    public String getCoursesNumber() {
        return coursesNumber;
    }

    public void setCoursesNumber(String coursesNumber) {
        this.coursesNumber = coursesNumber;
    }

    public String getTextbooksNumber() {
        return textbooksNumber;
    }

    public void setTextbooksNumber(String textbooksNumber) {
        this.textbooksNumber = textbooksNumber;
    }

    public String getKnowledgeNumber() {
        return knowledgeNumber;
    }

    public void setKnowledgeNumber(String knowledgeNumber) {
        this.knowledgeNumber = knowledgeNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("majorId", getMajorId())
            .append("name", getName())
            .append("sex", getSex())
            .append("title", getTitle())
            .append("education", getEducation())
            .append("researchDirection", getResearchDirection())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
