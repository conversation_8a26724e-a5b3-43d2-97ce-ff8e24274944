package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 模型训练任务对象 s_model_training
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class ModelTraining extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 任务ID */
    @Excel(name = "任务ID")
    private String taskId;

    /** 作业ID */
    @Excel(name = "作业ID")
    private String jobId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String description;

    /** 基础任务ID */
    @Excel(name = "基础任务ID")
    private String incrementTaskId;

    /** 迭代轮次 */
    @Excel(name = "迭代轮次")
    private Long epoch;

    /** 批处理大小 */
    @Excel(name = "批处理大小")
    private Long batchSize;

    /** 学习率 */
    @Excel(name = "学习率")
    private String learningRate;

    /** 序列长度 */
    @Excel(name = "序列长度")
    private String maxSeqlen;

    /** 保存日志间隔 */
    @Excel(name = "保存日志间隔")
    private String loggingSteps;

    /** 预热比例 */
    @Excel(name = "预热比例")
    private String warmupRatio;

    /** 正则化系数 */
    @Excel(name = "正则化系数")
    private String weightDecay;

    /** LoRA 策略中的秩 */
    @Excel(name = "LoRA 策略中的秩")
    private Long loraRank;

    /** LoRA 所有线性层 */
    @Excel(name = "LoRA 所有线性层")
    private String loraAllLinear;

    /** 数据来源 */
    @Excel(name = "数据来源")
    private String sourceType;

    /** 数据集版本ID */
    @Excel(name = "数据集版本ID")
    private String datasetId;

    /** 数据集采样率 */
    @Excel(name = "数据集采样率")
    private Long samplingRate;

    /** 数据集版本bos地址 */
    @Excel(name = "数据集版本bos地址")
    private String versionBosUri;

    /** 数据拆分比例 */
    @Excel(name = "数据拆分比例")
    private Long splitRatio;

    /** 混合训练混入比例 */
    @Excel(name = "混合训练混入比例")
    private String corpusProportion;

    /** 任务状态 */
    @Excel(name = "任务状态")
    private String taskStatus;

    /**
     * 数据集名称
     */
    private String groupName;

    /**
     * 模型版本id
     */
    private String modelId;
    /**
     * 归属模型id
     */
    private String modelSetId;

    /**
     * 菜单路由
     */
    private String menuRouting;
    private String ak;
    private String sk;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }
    public void setJobId(String jobId)
    {
        this.jobId = jobId;
    }

    public String getJobId()
    {
        return jobId;
    }
    public void setTaskName(String taskName)
    {
        this.taskName = taskName;
    }

    public String getTaskName()
    {
        return taskName;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setIncrementTaskId(String incrementTaskId)
    {
        this.incrementTaskId = incrementTaskId;
    }

    public String getIncrementTaskId()
    {
        return incrementTaskId;
    }
    public void setEpoch(Long epoch)
    {
        this.epoch = epoch;
    }

    public Long getEpoch()
    {
        return epoch;
    }
    public void setBatchSize(Long batchSize)
    {
        this.batchSize = batchSize;
    }

    public Long getBatchSize()
    {
        return batchSize;
    }

    public String getLearningRate() {
        return learningRate;
    }

    public void setLearningRate(String learningRate) {
        this.learningRate = learningRate;
    }

    public void setLoraRank(Long loraRank)
    {
        this.loraRank = loraRank;
    }

    public Long getLoraRank()
    {
        return loraRank;
    }
    public void setLoraAllLinear(String loraAllLinear)
    {
        this.loraAllLinear = loraAllLinear;
    }

    public String getLoraAllLinear()
    {
        return loraAllLinear;
    }
    public void setSourceType(String sourceType)
    {
        this.sourceType = sourceType;
    }

    public String getMaxSeqlen() {
        return maxSeqlen;
    }

    public void setMaxSeqlen(String maxSeqlen) {
        this.maxSeqlen = maxSeqlen;
    }

    public String getLoggingSteps() {
        return loggingSteps;
    }

    public void setLoggingSteps(String loggingSteps) {
        this.loggingSteps = loggingSteps;
    }

    public String getWarmupRatio() {
        return warmupRatio;
    }

    public void setWarmupRatio(String warmupRatio) {
        this.warmupRatio = warmupRatio;
    }

    public String getWeightDecay() {
        return weightDecay;
    }

    public void setWeightDecay(String weightDecay) {
        this.weightDecay = weightDecay;
    }

    public String getSourceType()
    {
        return sourceType;
    }
    public void setDatasetId(String datasetId)
    {
        this.datasetId = datasetId;
    }

    public String getDatasetId()
    {
        return datasetId;
    }
    public void setSamplingRate(Long samplingRate)
    {
        this.samplingRate = samplingRate;
    }

    public Long getSamplingRate()
    {
        return samplingRate;
    }
    public void setVersionBosUri(String versionBosUri)
    {
        this.versionBosUri = versionBosUri;
    }

    public String getVersionBosUri()
    {
        return versionBosUri;
    }
    public void setSplitRatio(Long splitRatio)
    {
        this.splitRatio = splitRatio;
    }

    public Long getSplitRatio()
    {
        return splitRatio;
    }
    public void setCorpusProportion(String corpusProportion)
    {
        this.corpusProportion = corpusProportion;
    }

    public String getCorpusProportion()
    {
        return corpusProportion;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getModelSetId() {
        return modelSetId;
    }

    public void setModelSetId(String modelSetId) {
        this.modelSetId = modelSetId;
    }

    public String getMenuRouting() {
        return menuRouting;
    }

    public void setMenuRouting(String menuRouting) {
        this.menuRouting = menuRouting;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }
}
