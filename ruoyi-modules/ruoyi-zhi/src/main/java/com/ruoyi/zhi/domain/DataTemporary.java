package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 临时数据集对象 s_data_temporary
 * 
 * <AUTHOR>
 * @date 2024-05-28
 */
public class DataTemporary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 数据集名称 */
    @Excel(name = "数据集名称")
    private String groupName;

    /** 数据类型  4-文本,7-跨模态 */
    @Excel(name = "数据类型  4-文本,7-跨模态")
    private Long dataType;

    /** 标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片 */
    @Excel(name = "标注类型 20-Prompt+Response / Prompt+多Response排序,401-纯文本,402-Prompt集,05-Prompt集+图片")
    private Long projectType;

    /** 标注模版 2000-Prompt+Response,2001-Prompt+多Response排序,40100-纯文本,40200-Prompt集,70500-Prompt集+图片 */
    @Excel(name = "标注模版 2000-Prompt+Response,2001-Prompt+多Response排序,40100-纯文本,40200-Prompt集,70500-Prompt集+图片")
    private Long templateType;

    private String entityCount;

    /**
     * 菜单路由
     */
    public String menuRouting;

    /**
     * 导入状态
     */
    private String importProgress;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }
    public void setDataType(Long dataType) 
    {
        this.dataType = dataType;
    }

    public Long getDataType() 
    {
        return dataType;
    }
    public void setProjectType(Long projectType) 
    {
        this.projectType = projectType;
    }

    public Long getProjectType() 
    {
        return projectType;
    }
    public void setTemplateType(Long templateType) 
    {
        this.templateType = templateType;
    }

    public Long getTemplateType() 
    {
        return templateType;
    }

    public String getImportProgress() {
        return importProgress;
    }

    public void setImportProgress(String importProgress) {
        this.importProgress = importProgress;
    }

    public String getEntityCount() {
        return entityCount;
    }

    public void setEntityCount(String entityCount) {
        this.entityCount = entityCount;
    }

    public String getMenuRouting() {
        return menuRouting;
    }

    public void setMenuRouting(String menuRouting) {
        this.menuRouting = menuRouting;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupName", getGroupName())
            .append("dataType", getDataType())
            .append("projectType", getProjectType())
            .append("templateType", getTemplateType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
