package com.ruoyi.zhi.vo;

import com.ruoyi.zhi.domain.UnionCourses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 联盟课程对象 vo
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
@ApiModel(description = "联盟课程对象vo")
public class UnionCoursesVo extends UnionCourses {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 联盟名称
     */
    @ApiModelProperty("联盟名称")
    private String name;

    /**
     * 联盟学校ids
     */
    @ApiModelProperty("联盟学校ids")
    private String schoolIds;
    private String[] schoolIdAray;

    /**
     * 联盟学校联盟学校names
     */
    @ApiModelProperty("联盟学校names")
    private String schoolNames;
    /**
     * 联盟学校schoolDataMapList
     */
    @ApiModelProperty("联盟学校schoolDataMapList")
    private List<Map<String, Object>> schoolDataMapList;

    /**
     * 课程名字
     */
    @ApiModelProperty("课程名字")
    private String courseName;


    /**
     * 课程成员userIds
     **/
    @ApiModelProperty("课程成员userIds")
    private String userIds;

    /**
     * 课程成员userNames
     **/
    @ApiModelProperty("课程成员userNames")
    private String userNames;
    /**
     * 课程成员nickNames
     **/
    @ApiModelProperty("课程成员nickNames")
    private String nickNames;


    @ApiModelProperty("书籍名称ID对 bookNameIdPairs")
    private String bookNameIdPairs;

//    @ApiModelProperty("教材列表knowledgeBaseVoList")
//    private List<KnowledgeBaseFileVo> knowledgeBaseVoList;
//
//    @ApiModelProperty("用户列表systemUserVoList")
//    private List<SystemUserVo> systemUserVoList;
//
//    @ApiModelProperty("学校列表universityList")
//    private List<University> universityList;

    /**
     * 节点数据列表
     */
    @ApiModelProperty("节点数据列表")
    private List<NodesDatasVo> nodesDatasVoList;

    /**
     * 连接数据列表
     */
    @ApiModelProperty("连接数据列表")
    private List<NodeDataLinksVo> nodeDataLinksVoList;

    /**
     * 创建者名字
     */
    @ApiModelProperty("创建者名字")
    private String createdByNickName;

    /**
     * 前端传递的 keywordLabel 字段搜索值
     */
    private String keywordLabel;
    /**
     * 前端传递的 keywordValue 字段搜索值
     */
    private String keywordValue;
    /**
     * 前端传递的 gradeFlag 等级标志搜索值
     */
    private String gradeFlag;

    /**
     * 对应表名_字段名
     **/
    private String tableNameFieldName;

    private String parentKeywordLabel;
    private String parentKeywordValue;
    private String parentFlag;


    // 前端传递要搜索的教材文件名
    private String fileName;
    // 前端传递要搜索的教师姓名
    private String nickName;
    private String userName;


}


