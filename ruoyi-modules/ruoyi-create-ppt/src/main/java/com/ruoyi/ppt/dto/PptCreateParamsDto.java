package com.ruoyi.ppt.dto;


import com.ruoyi.ppt.domain.PptTemplateManagement;
import com.ruoyi.ppt.domain.PptTemplateManagementDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * ppt生成的需要的最终参数
 */
@Data
public class PptCreateParamsDto {

    /**
     * 格式化后的文本路径
     */
    String formateTextfilePath;

    /**
     * 模板目录文件夹路径
     */
    String themeFolderBasePath;

    /**
     * 输出ppt文件夹目录
     */
    String outFileFolder;

    // 指定主题的模板管理
    private PptTemplateManagement pptTemplateManagement;

    // 指定主题的模板管理详情
    private List<Map<String, String>> templateManagementDetailMapList;
    private ArrayList<PptTemplateManagementDetail> pptTemplateManagementDetails;
}
