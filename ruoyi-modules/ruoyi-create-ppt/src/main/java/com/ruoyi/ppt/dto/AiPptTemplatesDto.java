package com.ruoyi.ppt.dto;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

@Data
@Getter
public class AiPptTemplatesDto  implements Serializable {
    private static final long serialVersionUID = 1L;
    private int page;
    private int size;
    private Filters filters;

    private String pptToken; // aiPpt 通过uid创建的隔离的用户的token 有效期 2小时

    private  String id;
    private  String name;

    @Data
    @Getter
    public static class Filters implements Serializable {
        private static final long serialVersionUID = 1L;
            private int type = 1; // 模板类型（必传）：1系统模板、4用户自定义模板
        private String category; // 类目筛选
        private String style; // 风格筛选
        private String themeColor; // 主题颜色筛选
    }

    @Data
    @Getter
    public static class Template implements Serializable {
        private String id;          // 模板ID
        private Integer type;       // 模板类型：1大纲完整PPT、4用户模板
        private String coverUrl;    // 封面（需要拼接?token=${token}访问）
        private String category;    // 类目
        private String style;       // 风格
        private String themeColor;  // 主题颜色
        private String subject;     // 主题
        private Integer num;        // 模板页数
        private String createTime;  // 创建时间（格式：yyyy-MM-dd HH:mm:ss）
        private String name; // 模板名称
        private List<String> pageCoverUrls; // 模板页封面列表
    }
}

