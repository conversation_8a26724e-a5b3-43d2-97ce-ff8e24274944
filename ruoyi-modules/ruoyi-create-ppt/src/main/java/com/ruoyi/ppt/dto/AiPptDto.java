package com.ruoyi.ppt.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class AiPptDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private String uid; // 用户ID，不同uid创建的token数据会相互隔离，主要用于数据隔离
    private Integer limit; // 限制 token 最大生成PPT次数
    private Integer timeOfHours = 2;   // 过期时间，单位：小时 默认两小时过期，最大可设置为48小时
    private String timestamp; // 时间戳
    private String pptToken; // aiPpt 通过uid创建的隔离的用户的token
    private String aiPptApiKey; // aiPptApiKey
    private String taskId;


    //1.智能生成（主题、要求）2.上传文件生成 3.上传思维导图生成 https://open.docmee.cn/open/doc/api#%E5%88%9B%E5%BB%BA%E4%BB%BB%E5%8A%A1
    // 4.通过 word 精准转 ppt5.通过网页链接生成6.粘贴文本内容生成7.Markdown 大纲生成
    private Integer type = 1;
    private String content = "";


    private String subject = ""; // ppt主题
    private String name = ""; // ppt 名称
    private String id = ""; // ppt id





}
