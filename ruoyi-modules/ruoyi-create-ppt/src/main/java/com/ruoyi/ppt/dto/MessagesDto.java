package com.ruoyi.ppt.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor()
public class MessagesDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private String role; // user  assistant
    private String resType; // answer thinking finish
    private String content;
    private String sessionId;
    private String messageId;
    private String dialogueId;

    private String requestId;

	private List<ReferencesDto> referencesList;


    private String status; // 保证和aippt 生成的大纲流式格式一致
    private String text; // 保证和aippt 生成的大纲流式格式一致
}

