package com.ruoyi.ppt.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class AiPptTemplateDto implements Serializable {

    private String id;          // 模板ID
    private Integer type;       // 模板类型：1大纲完整PPT、4用户模板
    private String coverUrl;    // 封面（需要拼接?token=${token}访问）
    private String category;    // 类目
    private String style;       // 风格
    private String themeColor;  // 主题颜色
    private String subject;     // 主题
    private Integer num;        // 模板页数
    private String createTime;  // 创建时间（格式：yyyy-MM-dd HH:mm:ss）


    private String pptxProperty;  // PPT数据结构（json gzip base64）

    private String pptToken; // aiPpt 通过uid创建的隔离的用户的token
}
