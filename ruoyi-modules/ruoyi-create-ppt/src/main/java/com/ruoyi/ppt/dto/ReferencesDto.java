package com.ruoyi.ppt.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor()
public class ReferencesDto implements Serializable {
    private static final long serialVersionUID = 1L;
  	private String id;
  	private String icon;
  	private String title;
  	private String url;
  	private String web_anchor;
  	private String content;
  	private String date;
  	private String type;
  	private String image;
  	private String video;

}
