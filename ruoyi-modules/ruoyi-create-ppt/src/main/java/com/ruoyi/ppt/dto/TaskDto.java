package com.ruoyi.ppt.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.File;

/**
 * 创建任务
 */
@Data
public class TaskDto {

    /**
     * 1.智能生成（主题、要求）
     * 2.上传文件生成
     * 3.上传思维导图生成
     * 4.通过 word 精准转 ppt
     * 5.通过网页链接生成
     * 6.粘贴文本内容生成
     * 7.Markdown 大纲生成
     */
    private Integer type;

    /**
     * type=1 用户输入主题或要求（不超过 1000 字符）
     * type=2、4 不传
     * type=3 幕布等分享链接
     * type=5 网页链接地址（http/https）
     * type=6 粘贴文本内容（不超过 20000 字符）
     * type=7 大纲内容（markdown）
     */
    private String content;

    /**
     * 文件列表（文件数不超过 5 个，总大小不超过 50M）：
     * type=1 上传参考文件（非必传，支持多个）
     * type=2 上传文件（支持多个）
     * type=3 上传思维导图（xmind/mm/md）（仅支持一个）
     * type=4 上传 word 文件（仅支持一个）
     * type=5、6、7 不传
     *
     * 支持格式：doc/docx/pdf/ppt/pptx/txt/md/xls/xlsx/csv/html/epub/mobi/xmind/mm
     */
    private File[] file;

}
