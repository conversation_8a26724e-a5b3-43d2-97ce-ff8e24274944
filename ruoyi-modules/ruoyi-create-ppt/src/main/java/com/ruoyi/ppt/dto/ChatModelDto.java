package com.ruoyi.ppt.dto;

import com.ruoyi.ppt.enmu.LanguageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatModelDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String content;

    private String type;
}
