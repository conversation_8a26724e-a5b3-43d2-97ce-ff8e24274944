package com.ruoyi.ppt.dto;

import java.util.List;

public class SplitResultDto {
    private final List<List<String>> splitResult;
    private final List<Integer> startIndices;

    public SplitResultDto(List<List<String>> splitResult, List<Integer> startIndices) {
        this.splitResult = splitResult;
        this.startIndices = startIndices;
    }

    public List<List<String>> getSplitResult() {
        return splitResult;
    }

    public List<Integer> getStartIndices() {
        return startIndices;
    }
}
