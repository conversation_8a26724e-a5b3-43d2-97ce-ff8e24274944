package com.ruoyi.ppt.vo;

import lombok.Data;

import java.util.List;

// 响应体基础结构
@Data
public class PPTComponentVo {
    private String type; // "cover"/"content"/"transition" end
    private Object data;
    
    // 各类型数据结构
    public static class CoverData {
        private String title;
        private String text;
    }
    
    public static class ContentData {
        private String title;
        private List<ContentItem> items;
    }
    
    public static class ContentItem {
        private String title;
        private String text;
    }
}