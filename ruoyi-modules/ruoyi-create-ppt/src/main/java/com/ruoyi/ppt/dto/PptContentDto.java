package com.ruoyi.ppt.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PptContentDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String content; // 主题内容

    @NotNull(message = "语言不能为空")
    private String language = "zh"; // 默认值zh

    @NotNull(message = "模型不能为空")
    private String model = "qianfan-deepseek-v3"; // 默认值qianfan-deepseek-v3

    @NotNull(message = "流式模式不能为空")
    private String stream = "true"; // 默认值true
}
