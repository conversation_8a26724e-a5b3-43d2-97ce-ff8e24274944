package com.ruoyi.ppt.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 创建PPT任务请求DTO
 */
@Data
public class PPTTaskCreateDto {
    /**
     * 1.智能生成（主题、要求）
     * 2.上传文件生成
     * 3.上传思维导图生成
     * 4.通过 word 精准转 ppt
     * 5.通过网页链接生成
     * 6.粘贴文本内容生成
     * 7.Markdown 大纲生成
     */
    private Integer type;

    /**
     * type=1 用户输入主题或要求（不超过 1000 字符）
     * type=2、4 不传
     * type=3 幕布等分享链接
     * type=5 网页链接地址（http/https）
     * type=6 粘贴文本内容（不超过 20000 字符）
     * type=7 大纲内容（markdown）
     */
    private String content;

    /**
     * 主题（支持中英文，例如：简约、科技、商务等）
     */
    private String theme;

    /**
     * 模板ID（平台内置或自定义模板ID）
     */
    private String templateId;

    /**
     * 中文：zh，英文：en，默认根据内容自动识别
     */
    private String language;

    /**
     * 16:9 或 4:3，默认 16:9
     */
    private String ratio;

    /**
     * 元素类型（例如：图表、图片、图标、表格、代码等），多个使用逗号分隔
     */
    private String elements;

    /**
     * 是否显示页码，0否，1是，默认1
     */
    private Integer showNumber;

    /**
     * logo图片（base64格式，大小不超过1M）
     */
    private String logoImage;

    /**
     * logo位置，可选值：topLeft、topCenter、topRight、bottomLeft、bottomCenter、bottomRight
     */
    private String logoPosition;

    /**
     * 水印文本（不超过 20 字符）
     */
    private String watermarkText;

    /**
     * logo宽度px，默认48
     */
    private Integer width;

    /**
     * logo高度px，默认48
     */
    private Integer height;

    /**
     * 文件列表，通过MultipartFile传输
     */
    private MultipartFile[] files;
} 