package com.ruoyi.ppt.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnterCreatePPTPageDto {

    private String apiKey;

    private String content; // 内容

    private String redirectUrl; // url

    private String type; // text file topic

    private String uid; // 内容uid

    private String userId; // 用户唯一标识

    private Integer duration = 2 * 60; // 有效时间分钟 默认 1 小时, 最大48 * 60分钟

}
