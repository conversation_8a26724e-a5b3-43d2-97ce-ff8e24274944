package com.ruoyi.ppt.dto;

import lombok.Data;

import java.util.List;

/**
 * PowerPoint模型数据传输对象（DTO）。
 * 用于封装PowerPoint的相关信息，包括大标题、章节和页面信息。
 */
@Data
public class PptModelDto {
    /**
     * PowerPoint的大标题。
     * 用于概括整个PowerPoint的主题。
     */
    String bigTitle;

    /**
     * PowerPoint的章节。
     * 用于区分PowerPoint的不同部分或主题。
     */
    String chapter;

    /**
     * PowerPoint的页面列表。
     * 包含所有的页面信息，每个页面由PptPageDto对象表示。
     */
    List<PptPageDto> pptPageDtos;

    public PptModelDto() {}

    public PptModelDto(String bigTitle, String chapter, List<PptPageDto> pptPageDtos) {
        this.bigTitle = bigTitle;
        this.chapter = chapter;
        this.pptPageDtos = pptPageDtos;
    }
}
