package com.ruoyi.ppt.dto;

import lombok.Data;

import java.util.ArrayList;
/**
 * PptPageDtotmp 类用于表示PPT页面的临时数据模型。
 * 它包含了页面的章节信息、大标题、多个小标题以及多个文本内容。
 */
@Data
public class PptPageDtotmp {

    /**
     * 章节名称，用于标识页面所属的章节。
     */
    String chapter;

    /**
     * 页面的大标题，用于概括页面的主要内容。
     */
    String bigTitle;

    /**
     * 小标题列表，用于列出页面中的多个小标题。
     * 每个小标题代表了页面中的一个细分内容。
     */
    ArrayList<String> littleTitles;

    /**
     * 文本内容列表，用于存储页面中的多个文本块。
     * 每个文本块代表了页面中的一个具体信息或细节。
     */
    ArrayList<String> texts;

    /**
     * 清空当前页面数据的方法。
     * 该方法将大标题置为空，清空小标题和文本内容列表。
     * 用于在重新填充页面数据前清除旧数据。
     */
    public void clear(){
        this.bigTitle=null;
        this.littleTitles.clear();
        this.texts.clear();
    }
}
