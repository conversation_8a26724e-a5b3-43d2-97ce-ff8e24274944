package com.ruoyi.ppt.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * PowerPoint页面数据传输对象类
 * 用于封装PowerPoint页面的相关信息，以供数据传输和序列化使用。
 */
@Data
public class PptPageDto implements Serializable {
    /**
     * 页面标题
     * 用于显示在PowerPoint页面上的标题内容。
     */
    String pageTitle;

    /**
     * 页面摘要
     * 简要概括页面内容，用于快速了解页面主题。
     */
    String pageSumr;

    /**
     * 列表标题集合
     * 包含页面中所有列表的标题，用于展示或检索列表内容。
     */
    List<String> litTitles;

    /**
     * 文本内容集合
     * 存储页面中的文本内容，可以是段落、列表项等。
     */
    List<String> texts;




    public PptPageDto() {
        this.litTitles = new ArrayList<>();
        this.texts = new ArrayList<>();
    }

    public PptPageDto(List<String> litTitles, List<String> texts, String pageTitle, String pageSumr) {
        this.litTitles = litTitles;
        this.texts = texts;
        this.pageTitle = pageTitle;
        this.pageSumr = pageSumr;
    }

}
