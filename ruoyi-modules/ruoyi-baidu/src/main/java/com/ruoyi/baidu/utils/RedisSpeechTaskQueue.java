package com.ruoyi.baidu.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.baidu.domain.SpeechTask;
import com.ruoyi.baidu.domain.TaskResult;
import com.ruoyi.baidu.dto.DBSpeechSynthesisDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的分布式语音合成任务队列
 */
@Component
public class RedisSpeechTaskQueue {
    private static final Logger log = LoggerFactory.getLogger(RedisSpeechTaskQueue.class);

    private static final String QUEUE_KEY = "speech:synthesis:queue";
    private static final String RESULT_KEY_PREFIX = "speech:synthesis:result:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 提交任务并等待结果（同步方式）
     * @param dto 语音合成参数
     * @param timeout 最大等待时间
     * @param unit 时间单位
     * @return 任务结果，如果超时返回null
     */
    public TaskResult submitAndWait(DBSpeechSynthesisDto dto, long timeout, TimeUnit unit) {
        // 生成唯一任务ID
        String taskId = UUID.randomUUID().toString();
        // 创建任务对象
        SpeechTask task = new SpeechTask(taskId, dto);
        try {
            // 创建结果键，用于等待结果
            String resultKey = RESULT_KEY_PREFIX + taskId;
            // 序列化任务并添加到队列
            String taskJson = objectMapper.writeValueAsString(task);
            redisTemplate.opsForList().rightPush(QUEUE_KEY, taskJson);

            log.info("任务已提交并等待结果，任务ID: {}", taskId);
            // 等待结果，使用Redis的阻塞操作
            long startTime = System.currentTimeMillis();
            long remainingTime = unit.toMillis(timeout);

            while (remainingTime > 0) {
                // 检查结果是否已经可用
                String resultJson = redisTemplate.opsForValue().get(resultKey);
                if (resultJson != null) {
                    // 结果已可用，解析并返回
                    TaskResult result = objectMapper.readValue(resultJson, TaskResult.class);
                    log.info("获取到任务结果，任务ID: {}, 等待时间: {}ms",
                            taskId, System.currentTimeMillis() - startTime);
                    return result;
                }

                // 短暂休眠，避免频繁查询Redis
                Thread.sleep(200);
                // 更新剩余等待时间
                remainingTime = unit.toMillis(timeout) - (System.currentTimeMillis() - startTime);
            }

            // 超时，返回null
            log.warn("等待任务结果超时，任务ID: {}", taskId);
            return null;
        } catch (Exception e) {
            log.error("提交任务并等待结果失败", e);
            throw new RuntimeException("处理请求失败", e);
        }
    }

    /**
     * 将语音合成任务添加到队列 ： 无法获取到信号量后，传入dto信息重新入队，并生产新的任务ID
     * @param dto 语音合成参数
     * @return 任务ID，用于后续查询结果
     */
    public String enqueueTask(DBSpeechSynthesisDto dto) {
        try {
            // 生成唯一任务ID
            String taskId = UUID.randomUUID().toString();
            // 创建任务对象
            SpeechTask task = new SpeechTask(taskId, dto);
            // 序列化任务并添加到队列
            String taskJson = objectMapper.writeValueAsString(task);
            // 入队 -》从队列右侧入队
            redisTemplate.opsForList().rightPush(QUEUE_KEY, taskJson);
            log.info("任务已添加到队列，任务ID: {}", taskId);
            return taskId;
        } catch (JsonProcessingException e) {
            log.error("任务序列化失败", e);
            throw new RuntimeException("添加任务到队列失败", e);
        }
    }

    /**
     * 从队列中获取任务（阻塞操作，超时返回null）
     * @param timeout 超时时间（秒）
     * @return 任务对象，如果超时返回null
     */
    public SpeechTask dequeueTask(long timeout) {
        try {
            // 阻塞式获取任务  从队列左侧弹出数据 查询键 等待时间 时间单位
            String taskJson = redisTemplate.opsForList().leftPop(QUEUE_KEY, timeout, TimeUnit.SECONDS);
            // 不存在数据 返回null
            if (taskJson == null) {
                return null;
            }
            // 存在数据 将数据 反序列化任务
            SpeechTask task = objectMapper.readValue(taskJson, SpeechTask.class);
            log.info("从队列获取到任务，任务ID: {}", task.getTaskId());
            return task;
        } catch (IOException e) {
            log.error("任务反序列化失败", e);
            return null;
        }
    }


    /**
     * 保存任务处理结果
     * @param taskResult 任务结果对象
     */
    public void saveTaskResult(TaskResult taskResult) {
        try {
            String resultJson = objectMapper.writeValueAsString(taskResult);
            String resultKey = RESULT_KEY_PREFIX + taskResult.getTaskId();

            // 保存结果，设置10分钟过期
            redisTemplate.opsForValue().set(resultKey, resultJson, 10, TimeUnit.MINUTES);
            log.info("任务结果已保存，任务ID: {}", taskResult.getTaskId());
        } catch (JsonProcessingException e) {
            log.error("保存任务结果失败", e);
        }
    }


    /**
     * 获取队列长度
     * @return 队列中等待处理的任务数量
     */
    public long getQueueSize() {
        Long size = redisTemplate.opsForList().size(QUEUE_KEY);
        return size != null ? size : 0;
    }


}
