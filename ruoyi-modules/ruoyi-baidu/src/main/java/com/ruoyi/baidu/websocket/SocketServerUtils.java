package com.ruoyi.baidu.websocket;

import org.java_websocket.client.WebSocketClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.CopyOnWriteArraySet;
import java.nio.ByteBuffer;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.baidu.service.IBaDuTextToSpeechService;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@ServerEndpoint("/yRSocketServer")
@Component
public class SocketServerUtils {

    private static final Logger log = LoggerFactory.getLogger(SocketServerUtils.class);
    static final CopyOnWriteArraySet<SocketServerUtils> clients = new CopyOnWriteArraySet<>();
    Session session;
    private WebSocketClient externalClient;

    // 注意：由于WebSocket是多例的，需要使用静态注入
    private static IBaDuTextToSpeechService baDuTextToSpeechService;

    @Autowired
    public void setBaDuTextToSpeechService(IBaDuTextToSpeechService service) {
        SocketServerUtils.baDuTextToSpeechService = service;
    }

    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        clients.add(this);
        // 设置会话配置
        session.setMaxIdleTimeout(3600000); // 1小时超时
        // 配置最大消息大小（如果需要传输大文件）
        session.setMaxBinaryMessageBufferSize(1024 * 1024);
        session.setMaxTextMessageBufferSize(1024 * 1024);

        // 生成唯一的sn参数
        String sn = "RUOYI-" + UUID.randomUUID().toString().substring(0, 10);
        // 连接到百度语音识别服务
        externalClient = SocketResultUtils.getClient("wss://vop.baidu.com/realtime_asr?sn=" + sn);
        if (externalClient != null) {
            try {
                externalClient.connectBlocking(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("外部WebSocket客户端未连接或已关闭");
            }
        }

        log.info("WebSocket连接建立 - 当前连接数: {}", clients.size());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到文本消息: {}", message);
        try {
            // 判断是否是START消息
            JSONObject jsonObj = new JSONObject(message);
            if ("START".equals(jsonObj.optString("type"))) {
                // 如果是START消息，需要添加token
                JSONObject data = jsonObj.getJSONObject("data");
                // 获取百度语音识别token
                String token = baDuTextToSpeechService.getBaDuASRToken().get("token").toString();
                data.put("token", token);
                jsonObj.put("data", data);
                message = jsonObj.toString();
            }

            // 转发消息到百度服务
            if (externalClient != null && externalClient.isOpen()) {
                externalClient.send(message);
                log.info("转发文本消息到百度服务: {}", message);
            } else {
                log.error("外部WebSocket客户端未连接或已关闭");
                session.getBasicRemote().sendText("{\"error\":\"外部服务连接失败\"}");
            }
        } catch (Exception e) {
            log.error("处理文本消息时发生错误", e);
            try {
                session.getBasicRemote().sendText("{\"error\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("发送错误消息失败", ex);
            }
        }
    }

    @OnMessage
    public void onMessage(byte[] message) {
        log.info("收到二进制消息，长度: {}", message.length);
        try {
            // 转发二进制消息到百度服务
            if (externalClient != null && externalClient.isOpen()) {
                externalClient.send(message);
                log.info("转发二进制消息到百度服务，长度: {}", message.length);
            } else {
                log.error("外部WebSocket客户端未连接或已关闭");
                session.getBasicRemote().sendText("{\"error\":\"外部服务连接失败\"}");
            }
        } catch (Exception e) {
            log.error("处理二进制消息时发生错误", e);
            try {
                session.getBasicRemote().sendText("{\"error\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("发送错误消息失败", ex);
            }
        }
    }

    @OnClose
    public void onClose() {
        clients.remove(this);
        // 关闭与百度服务的连接
        if (externalClient != null) {
            externalClient.close();
        }
        log.info("WebSocket连接关闭 - 当前连接数: {}", clients.size());
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误: {}", error.getMessage(), error);
        try {
            if (session.isOpen()) {
                session.close();
            }
        } catch (IOException e) {
            log.error("关闭出错的WebSocket连接时发生异常", e);
        }
    }

    // 添加心跳检测方法
    public void sendPing() {
        try {
            if (session.isOpen()) {
                session.getBasicRemote().sendPing(ByteBuffer.wrap("ping".getBytes()));
            }
        } catch (IOException e) {
            log.error("发送ping消息失败", e);
        }
    }
}
