package com.ruoyi.baidu.websocket;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.net.URI;
import java.nio.ByteBuffer;
import org.springframework.stereotype.Component;
import com.google.gson.JsonObject;
import okhttp3.*;
import okio.ByteString;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

@Component
public class DBSocketResultUtils {

    private static final Logger log = LoggerFactory.getLogger(DBSocketResultUtils.class);

    // 豆包语音识别服务的访问密钥
    private static final String APP_ID = "6270027884";
    private static final String TOKEN = "ZoHNvudALt0n7SegmWisX36A1wDpQ7eZ";

    // 协议常量
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    // Message Type:
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    // Message Type Specific Flags
    private static final byte NO_SEQUENCE = 0b0000;
    static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_SEQUENCE = 0b0010;
    static final byte NEG_WITH_SEQUENCE = 0b0011;
    // Message Serialization
    static final byte NO_SERIALIZATION = 0b0000;
    private static final byte JSON = 0b0001;
    // Message Compression
    static final byte NO_COMPRESSION = 0b0000;
    private static final byte GZIP = 0b0001;

    public static WebSocket getClient(String uri) {
        try {
            log.info("创建WebSocket客户端连接到豆包语音识别服务: {}", uri);

            /**
             * X-Api-Resource-Id 参数
             * 小时版：volc.bigasr.sauc.duration
             * 并发版：volc.bigasr.sauc.concurrent
             */
            final Request request = new Request.Builder()
                    .url(uri)
                    .header("X-Api-App-Key", APP_ID)
                    .header("X-Api-Access-Key", TOKEN)
                    .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                    .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                    .build();

            final OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .pingInterval(50, TimeUnit.SECONDS)
                    .readTimeout(100, TimeUnit.SECONDS)
                    .writeTimeout(100, TimeUnit.SECONDS)
                    .build();

            return okHttpClient.newWebSocket(request, new WebSocketListener() {
                private int seq = 0;

                @Override
                public void onOpen(WebSocket webSocket, Response response) {
                    log.info("已连接到豆包语音识别服务");
                    String logId = response.header("X-Tt-Logid");
                    log.info("X-Tt-Logid: {}", logId);
                    // 发送初始化参数
                    sendInitParams(webSocket);
                }

                @Override
                public void onMessage(WebSocket webSocket, String text) {
                    log.info("从豆包服务收到文本消息: {}", text);
                }

                @Override
                public void onMessage(WebSocket webSocket, ByteString bytes) {
                    log.info("从豆包服务收到二进制消息，长度: {}", bytes.size());

                    try {
                        byte[] res = bytes.toByteArray();
                        // 结束响应
                        int sequence = parserResponse(res);
                        boolean isLastPackage = sequence < 0;

                        // 将解析后的结果转发回前端
                        for (DBSocketServerUtils client : DBSocketServerUtils.clients) {
                            try {
                                client.session.getAsyncRemote().sendText(getResponsePayload(res));
                            } catch (Exception e) {
                                log.error("转发消息到客户端失败", e);
                            }
                        }

                        if (isLastPackage) {
                            log.info("识别完成，收到最后一个包");
                        }
                    } catch (Exception e) {
                        log.error("处理豆包服务返回的消息失败", e);
                    }
                }

                @Override
                public void onClosing(WebSocket webSocket, int code, String reason) {
                    log.info("豆包服务正在关闭连接: code={}, reason={}", code, reason);
                    webSocket.close(1000, "客户端主动关闭");
                }

                @Override
                public void onClosed(WebSocket webSocket, int code, String reason) {
                    log.info("与豆包语音识别服务的连接已关闭: code={}, reason={}", code, reason);
                    // 通知前端连接已关闭
                    for (DBSocketServerUtils client : DBSocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendText("{\"type\":\"CLOSE\",\"code\":" + code + ",\"reason\":\"" + reason + "\"}");
                        } catch (Exception e) {
                            log.error("发送关闭通知到客户端失败", e);
                        }
                    }
                }

                @Override
                public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                    log.error("与豆包语音识别服务的连接发生错误: {}", t.getMessage(), t);
                    // 通知前端发生错误
                    for (DBSocketServerUtils client : DBSocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendText("{\"type\":\"ERROR\",\"message\":\"" + t.getMessage() + "\"}");
                        } catch (Exception e) {
                            log.error("发送错误通知到客户端失败", e);
                        }
                    }
                }

                private void sendInitParams(WebSocket webSocket) {
                    try {
                        // 构建初始化参数
                        JsonObject user = new JsonObject();
                        user.addProperty("uid", "user_" + System.currentTimeMillis());

                        JsonObject audio = new JsonObject();
                        audio.addProperty("format", "pcm");
                        audio.addProperty("sample_rate", 16000);
                        audio.addProperty("bits", 16);
                        audio.addProperty("channel", 1);
                        audio.addProperty("codec", "raw");

                        JsonObject request = new JsonObject();
                        request.addProperty("model_name", "bigmodel");
                        request.addProperty("enable_punc", true);

                        JsonObject payload = new JsonObject();
                        payload.add("user", user);
                        payload.add("audio", audio);
                        payload.add("request", request);

                        String payloadStr = payload.toString();
                        log.info("初始化参数: {}", payloadStr);

                        // 压缩payload
                        byte[] payloadBytes = gzipCompress(payloadStr.getBytes());

                        // 组装请求
                        byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
                        byte[] payloadSize = intToBytes(payloadBytes.length);
                        seq = 1;
                        byte[] seqBytes = intToBytes(seq);

                        byte[] fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
                        System.arraycopy(header, 0, fullClientRequest, 0, header.length);
                        System.arraycopy(seqBytes, 0, fullClientRequest, header.length, seqBytes.length);
                        System.arraycopy(payloadSize, 0, fullClientRequest, header.length + seqBytes.length, payloadSize.length);
                        System.arraycopy(payloadBytes, 0, fullClientRequest, header.length + seqBytes.length + payloadSize.length, payloadBytes.length);

                        // 发送请求
                        boolean success = webSocket.send(ByteString.of(fullClientRequest));
                        log.info("初始化参数发送{}", success ? "成功" : "失败");
                    } catch (Exception e) {
                        log.error("发送初始化参数失败", e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("创建WebSocket客户端失败", e);
            return null;
        }
    }

    /**
     * 生成请求头部
     */
    static byte[] getHeader(byte messageType, byte messageTypeSpecificFlags, byte serialMethod, byte compressionType,
                            byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (byte) ((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE); // Protocol version|header size
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags); // message type | messageTypeSpecificFlags
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }

    /**
     * 整数转字节数组
     */
    static byte[] intToBytes(int value) {
        byte[] src = new byte[4];
        src[0] = (byte) ((value >> 24) & 0xFF);
        src[1] = (byte) ((value >> 16) & 0xFF);
        src[2] = (byte) ((value >> 8) & 0xFF);
        src[3] = (byte) (value & 0xFF);
        return src;
    }

    /**
     * 字节数组转整数
     */
    private static int bytesToInt(byte[] src) {
        return ((src[0] & 0xff) << 24)
                | ((src[1] & 0xff) << 16)
                | ((src[2] & 0xff) << 8)
                | (src[3] & 0xff);
    }

    /**
     * GZIP压缩
     */
    private static byte[] gzipCompress(byte[] src) {
        if (src == null || src.length == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = null;
        try {
            gzip = new GZIPOutputStream(out);
            gzip.write(src);
        } catch (IOException e) {
            log.error("GZIP压缩失败", e);
        } finally {
            if (gzip != null) {
                try {
                    gzip.close();
                } catch (IOException e) {
                    log.error("关闭GZIP流失败", e);
                }
            }
        }
        return out.toByteArray();
    }

    /**
     * GZIP解压缩
     */
    private static byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPInputStream gzip = new GZIPInputStream(new java.io.ByteArrayInputStream(src))) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            log.error("GZIP解压缩失败", e);
        }
        return out.toByteArray();
    }

    /**
     * 解析响应
     */
    private static int parserResponse(byte[] res) {
        if (res == null || res.length == 0) {
            return -1;
        }

        final byte num = 0b00001111;

        // 解析头部
        int protocol_version = (res[0] >> 4) & num;
        int header_size = res[0] & 0x0f;
        int message_type = (res[1] >> 4) & num;
        int message_type_specific_flags = res[1] & 0x0f;
        int serialization_method = (res[2] >> 4) & num;
        int message_compression = res[2] & 0x0f;

        // 解析序列号
        byte[] temp = new byte[4];
        System.arraycopy(res, 4, temp, 0, temp.length);
        int sequence = bytesToInt(temp);

        // 解析有效载荷大小
        System.arraycopy(res, 8, temp, 0, temp.length);
        int payloadSize = bytesToInt(temp);

        // 提取有效载荷
        byte[] payload = new byte[res.length - 12];
        System.arraycopy(res, 12, payload, 0, payload.length);

        // 记录响应信息
        log.info("响应类型: {}, 序列号: {}, 有效载荷大小: {}", message_type, sequence, payloadSize);

        return sequence;
    }

    /**
     * 获取响应中的有效载荷
     */
    private static String getResponsePayload(byte[] res) {
        if (res == null || res.length < 12) {
            return "{}";
        }

        final byte num = 0b00001111;
        int message_type = (res[1] >> 4) & num;
        int message_compression = res[2] & 0x0f;

        // 提取有效载荷
        byte[] payload = new byte[res.length - 12];
        System.arraycopy(res, 12, payload, 0, payload.length);

        // 根据消息类型和压缩方式处理有效载荷
        if (message_type == FULL_SERVER_RESPONSE) {
            if (message_compression == GZIP) {
                return new String(gzipDecompress(payload));
            } else {
                return new String(payload);
            }
        } else if (message_type == SERVER_ACK) {
            return new String(payload);
        } else if (message_type == SERVER_ERROR_RESPONSE) {
            // 提取错误码
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int errorCode = bytesToInt(temp);

            return "{\"error\":true,\"code\":" + errorCode + ",\"message\":\"" + new String(payload) + "\"}";
        }

        return "{}";
    }
}
