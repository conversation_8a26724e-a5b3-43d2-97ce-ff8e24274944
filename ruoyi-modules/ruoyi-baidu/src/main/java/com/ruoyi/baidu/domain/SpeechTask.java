package com.ruoyi.baidu.domain;

import com.ruoyi.baidu.dto.DBSpeechSynthesisDto;

import java.io.Serializable;

/**
 * 语音合成任务类
 * 用于在分布式队列中传输任务信息
 */
public class SpeechTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一标识
     */
    private String taskId;

    /**
     * 语音合成参数
     */
    private DBSpeechSynthesisDto dto;

    /**
     * 任务提交时间戳
     */
    private long submitTime;

    public SpeechTask() {
    }

    public SpeechTask(String taskId, DBSpeechSynthesisDto dto) {
        this.taskId = taskId;
        this.dto = dto;
        this.submitTime = System.currentTimeMillis();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public DBSpeechSynthesisDto getDto() {
        return dto;
    }

    public void setDto(DBSpeechSynthesisDto dto) {
        this.dto = dto;
    }

    public long getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(long submitTime) {
        this.submitTime = submitTime;
    }


    @Override
    public String toString() {
        return "SpeechTask{" +
                "taskId='" + taskId + '\'' +
                ", submitTime=" + submitTime +
                '}';
    }
}
