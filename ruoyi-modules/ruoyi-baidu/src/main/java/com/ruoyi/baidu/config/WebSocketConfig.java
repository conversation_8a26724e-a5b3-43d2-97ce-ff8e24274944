package com.ruoyi.baidu.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
public class WebSocketConfig {

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 设置最大文本消息大小
        container.setMaxTextMessageBufferSize(8192);
        // 设置最大二进制消息大小
        container.setMaxBinaryMessageBufferSize(8192);
        // 设置异步发送超时时间
        container.setAsyncSendTimeout(5000L);
        // 设置会话空闲超时
        container.setMaxSessionIdleTimeout(3600000L);
        return container;
    }
}
