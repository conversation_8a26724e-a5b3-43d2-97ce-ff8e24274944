package com.ruoyi.baidu.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import okhttp3.WebSocket;
import okio.ByteString;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.CopyOnWriteArraySet;

@ServerEndpoint("/DBSocketServer")
@Component
public class DBSocketServerUtils {

    private static final Logger log = LoggerFactory.getLogger(DBSocketServerUtils.class);
    static final CopyOnWriteArraySet<DBSocketServerUtils> clients = new CopyOnWriteArraySet<>();
    Session session;
    private WebSocket externalClient;
    private int seq = 1; // 序列号，从1开始

    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        clients.add(this);
        // 设置会话配置
        session.setMaxIdleTimeout(1000*60*2); // 2分钟超时
        // 配置最大消息大小（如果需要传输大文件）
        session.setMaxBinaryMessageBufferSize(1024 * 1024);
        session.setMaxTextMessageBufferSize(1024 * 1024);

        // 连接到豆包语音识别服务
        externalClient = DBSocketResultUtils.getClient("wss://openspeech.bytedance.com/api/v3/sauc/bigmodel");
        if (externalClient == null) {
            try {
                session.getBasicRemote().sendText("{\"error\":\"无法连接到豆包语音识别服务\"}");
            } catch (IOException e) {
                log.error("发送错误消息失败", e);
            }
        }

        log.info("WebSocket连接建立 - 当前连接数: {}", clients.size());
    }

    @OnMessage
    public void onMessage(byte[] message) {
        log.info("收到二进制消息，长度: {}", message.length);
        try {
            // 转发二进制消息到豆包服务
            if (externalClient != null) {
                // 构建音频数据请求
                boolean isLast = false; // 根据实际情况判断是否是最后一段音频
                sendAudioData(message, isLast);
                log.info("转发二进制消息到豆包服务，长度: {}", message.length);
            } else {
                log.error("外部WebSocket客户端未连接或已关闭");
                session.getBasicRemote().sendText("{\"error\":\"外部服务连接失败\"}");
            }
        } catch (Exception e) {
            log.error("处理二进制消息时发生错误", e);
            try {
                session.getBasicRemote().sendText("{\"error\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("发送错误消息失败", ex);
            }
        }
    }

    @OnMessage
    public void onMessage(String message) {
        log.info("收到文本消息: {}", message);
        try {
            // 处理特殊命令，如结束识别
            if (message.equals("END_OF_SPEECH")) {
                // 发送最后一段音频数据（空数据）
                sendAudioData(new byte[0], true);
                log.info("发送结束识别信号");
            }
        } catch (Exception e) {
            log.error("处理文本消息时发生错误", e);
            try {
                session.getBasicRemote().sendText("{\"error\":\"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("发送错误消息失败", ex);
            }
        }
    }

    @OnClose
    public void onClose() {
        clients.remove(this);
        // 关闭与豆包服务的连接
        if (externalClient != null) {
            externalClient.close(1000, "客户端关闭连接");
        }
        log.info("WebSocket连接关闭 - 当前连接数: {}", clients.size());
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误: {}", error.getMessage(), error);
        try {
            if (session.isOpen()) {
                session.close();
            }
        } catch (IOException e) {
            log.error("关闭出错的WebSocket连接时发生异常", e);
        }
    }

    /**
     * 发送音频数据到豆包服务
     */
    private void sendAudioData(byte[] audioData, boolean isLast) {
        try {
            // 递增序列号
            seq++;
            if (isLast) {
                seq = -seq; // 最后一段音频使用负序列号
            }
            // 构建音频数据请求头
            byte messageTypeSpecificFlags = isLast ? DBSocketResultUtils.NEG_WITH_SEQUENCE : DBSocketResultUtils.POS_SEQUENCE;
            byte[] header = DBSocketResultUtils.getHeader(DBSocketResultUtils.AUDIO_ONLY_REQUEST, messageTypeSpecificFlags,
                    DBSocketResultUtils.NO_SERIALIZATION, DBSocketResultUtils.NO_COMPRESSION, (byte) 0);
            // 序列号
            byte[] seqBytes = DBSocketResultUtils.intToBytes(seq);
            // 有效载荷大小
            byte[] payloadSize = DBSocketResultUtils.intToBytes(audioData.length);
            // 组装请求
            byte[] request = new byte[header.length + seqBytes.length + payloadSize.length + audioData.length];
            System.arraycopy(header, 0, request, 0, header.length);
            System.arraycopy(seqBytes, 0, request, header.length, seqBytes.length);
            System.arraycopy(payloadSize, 0, request, header.length + seqBytes.length, payloadSize.length);
            System.arraycopy(audioData, 0, request, header.length + seqBytes.length + payloadSize.length, audioData.length);
            // 发送请求
            boolean success = externalClient.send(ByteString.of(request));
            log.info("音频数据发送{}, 序列号: {}, 长度: {}", success ? "成功" : "失败", seq, audioData.length);
        } catch (Exception e) {
            log.error("发送音频数据失败", e);
        }
    }

}
