package com.ruoyi.baidu.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的分布式信号量实现
 */
@Component
public class RedisSpeechSemaphore {
    private static final Logger log = LoggerFactory.getLogger(RedisSpeechSemaphore.class);

    private static final String SEMAPHORE_KEY = "speech:synthesis:semaphore";
    private static final String OWNER_KEY_PREFIX = "speech:synthesis:owner:";

    // 默认信号量大小（最大并发数）
    @Value("${speech.semaphore}")
    private int DEFAULT_PERMITS;

    // 默认获取信号量的超时时间（秒）
    private static final long DEFAULT_TIMEOUT = 30;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private DefaultRedisScript<Long> acquireScript;
    private DefaultRedisScript<Long> releaseScript;

    @PostConstruct
    public void init() {
        // 初始化信号量
        redisTemplate.opsForValue().set(SEMAPHORE_KEY, String.valueOf(DEFAULT_PERMITS));
        log.info("初始化分布式信号量，许可数: {}", DEFAULT_PERMITS);


        // 加载Lua脚本
        acquireScript = new DefaultRedisScript<>();
        acquireScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("scripts/acquire_semaphore.lua")));
        acquireScript.setResultType(Long.class);

        releaseScript = new DefaultRedisScript<>();
        releaseScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("scripts/release_semaphore.lua")));
        releaseScript.setResultType(Long.class);

        log.info("分布式信号量初始化完成");
    }

    /**
     * 尝试获取信号量许可
     * @return 许可ID，如果获取失败返回null
     */
    public String tryAcquire() {
        return tryAcquire(DEFAULT_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 尝试在指定时间内获取信号量许可
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 许可ID，如果获取失败返回null
     */
    public String tryAcquire(long timeout, TimeUnit unit) {
        String ownerId = generateOwnerId();
        long endTime = System.currentTimeMillis() + unit.toMillis(timeout);

        while (System.currentTimeMillis() < endTime) {
            // 执行获取信号量的Lua脚本
            Long result = redisTemplate.execute(
                    acquireScript,
                    Collections.singletonList(SEMAPHORE_KEY),
                    ownerId
            );

            if (result != null && result == 1) {
                log.info("成功获取信号量许可，ID: {}", ownerId);
                return ownerId;
            }

            try {
                // 短暂休眠后重试
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("获取信号量过程被中断");
                return null;
            }
        }

        log.warn("获取信号量超时");
        return null;
    }

    /**
     * 释放信号量许可
     * @param ownerId 许可ID
     * @return 是否成功释放
     */
    public boolean release(String ownerId) {
        if (ownerId == null) {
            return false;
        }

        // 执行释放信号量的Lua脚本
        Long result = redisTemplate.execute(
                releaseScript,
                Collections.singletonList(SEMAPHORE_KEY),
                ownerId
        );

        boolean success = result != null && result == 1;
        if (success) {
            log.info("成功释放信号量许可，ID: {}", ownerId);
        } else {
            log.warn("释放信号量失败，ID: {}", ownerId);
        }

        return success;
    }

    /**
     * 获取当前可用的许可数
     * @return 可用许可数
     */
    public int availablePermits() {
        String value = redisTemplate.opsForValue().get(SEMAPHORE_KEY);
        return value != null ? Integer.parseInt(value) : 0;
    }

    /**
     * 重置信号量大小
     * @param permits 新的许可数
     */
    public void setPermits(int permits) {
        redisTemplate.opsForValue().set(SEMAPHORE_KEY, String.valueOf(permits));
        log.info("重置分布式信号量大小为: {}", permits);
    }

    /**
     * 生成唯一的许可ID
     */
    private String generateOwnerId() {
        return OWNER_KEY_PREFIX + System.currentTimeMillis() + ":" + Thread.currentThread().getId();
    }
}
