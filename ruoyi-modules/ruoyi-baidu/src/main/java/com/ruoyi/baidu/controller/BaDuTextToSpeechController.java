package com.ruoyi.baidu.controller;

import com.ruoyi.baidu.domain.TaskResult;
import com.ruoyi.baidu.dto.DBSpeechSynthesisDto;
import com.ruoyi.baidu.dto.SpeechSynthesisDto;
import com.ruoyi.baidu.service.IBaDuTextToSpeechService;
import com.ruoyi.baidu.utils.RedisSpeechSemaphore;
import com.ruoyi.baidu.utils.RedisSpeechTaskQueue;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/BaiDuSpeech")
@Api(tags = "百度语音Token")
public class BaDuTextToSpeechController extends BaseController {

    @Autowired
    private IBaDuTextToSpeechService buDuApiService;

    @Autowired
    private RedisSpeechTaskQueue taskQueue;

    @RequestMapping("/getBaDuApiToken")
    @ApiOperation(value = "获取百度apiToken")
    public AjaxResult getBaDuApiToken(){
        return buDuApiService.getBaDuApiToken();
    }

    @RequestMapping("/getBaDuASRToken")
    @ApiOperation(value = "获取百度apiToken")
    public AjaxResult getBaDuASRToken(){
        return buDuApiService.getBaDuASRToken();
    }

    @RequestMapping("/BaiDuASR")
    @ApiOperation(value = "百度语音识别")
    public AjaxResult BaiDuASR(@RequestBody Map<String,String> option){
        String json = buDuApiService.BaiDuASR(option);
        String result = "";
        try {
            JSONObject jsonObject = new JSONObject(json);
            result = jsonObject.getJSONArray("result").getString(0);
        } catch (JSONException e) {
            throw new RuntimeException("识别失败");
        }
        return AjaxResult.success(result);
    }

    @PostMapping(value = "/YRTTS", produces = "audio/mp3")
    @ApiOperation(value = "文本转语音")
    public ResponseEntity<byte[]> textToSpeech(@RequestBody SpeechSynthesisDto speechSynthesisDto) {
    byte[] audioData = buDuApiService.YRTTS(speechSynthesisDto);

    if (audioData != null) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("audio/mp3"));
        headers.setContentLength(audioData.length);
        // 设置文件名
        String filename = "speech_" + System.currentTimeMillis() + ".mp3";
        headers.setContentDispositionFormData("attachment", filename);

            return new ResponseEntity<>(audioData, headers, HttpStatus.OK);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PostMapping("/DBTTS")
    @ApiOperation(value = "豆包文本转语音")
    public AjaxResult DBtextToSpeech(@Valid @RequestBody DBSpeechSynthesisDto DBSpeechSynthesisDto) {
        long startTime = System.currentTimeMillis();
        try {
            // 提交任务到队列并等待结果，最多等待60秒
            TaskResult result = taskQueue.submitAndWait(DBSpeechSynthesisDto, 60, TimeUnit.SECONDS);
            if (result == null) {
                // 等待超时
                return AjaxResult.error("处理请求超时，请稍后再试");
            }
            if (!result.isSuccess()) {
                // 处理失败
                return AjaxResult.error(result.getErrorMessage());
            }
            // 处理成功，构建响应对象
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("audioData", result.getResult());
            response.put("format", "mp3");
            response.put("timestamp", System.currentTimeMillis());
            response.put("processingTime", System.currentTimeMillis() - startTime);

            return AjaxResult.success(response);
        } catch (Exception e) {

            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * ue 调用豆包文本转语音输出音频文件到本地返回一个磁盘地址，文件名
     */
    @PostMapping("/DBTTSFile")
    @ApiOperation(value = "豆包文本转语音-ue端")
    public AjaxResult DBtextToSpeechFile(@Valid @RequestBody DBSpeechSynthesisDto DBSpeechSynthesisDto) {
        Map<String, Object> response = buDuApiService.DBtextToSpeechFile(DBSpeechSynthesisDto);
        return AjaxResult.success(response);
    }
}
