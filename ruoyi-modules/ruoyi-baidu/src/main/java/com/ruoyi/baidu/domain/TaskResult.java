package com.ruoyi.baidu.domain;

import java.io.Serializable;

/**
 * 任务结果类
 * 用于存储语音合成任务的处理结果
 */
public class TaskResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一标识
     */
    private String taskId;

    /**
     * 处理结果（通常是音频的base64编码）
     */
    private String result;

    /**
     * 是否处理成功
     */
    private boolean success;

    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;

    /**
     * 任务完成时间戳
     */
    private long completeTime;

    /**
     * 处理耗时（毫秒）
     */
    private long processingTime;

    public TaskResult() {
    }

    public TaskResult(String taskId) {
        this.taskId = taskId;
        this.completeTime = System.currentTimeMillis();
    }

    public static TaskResult success(String taskId, String result, long startTime) {
        TaskResult taskResult = new TaskResult(taskId);
        taskResult.setResult(result);
        taskResult.setSuccess(true);
        taskResult.setProcessingTime(System.currentTimeMillis() - startTime);
        return taskResult;
    }

    public static TaskResult failure(String taskId, String errorMessage, long startTime) {
        TaskResult taskResult = new TaskResult(taskId);
        taskResult.setSuccess(false);
        taskResult.setErrorMessage(errorMessage);
        taskResult.setProcessingTime(System.currentTimeMillis() - startTime);
        return taskResult;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public long getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(long completeTime) {
        this.completeTime = completeTime;
    }

    public long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(long processingTime) {
        this.processingTime = processingTime;
    }

    @Override
    public String toString() {
        return "TaskResult{" +
                "taskId='" + taskId + '\'' +
                ", success=" + success +
                ", completeTime=" + completeTime +
                ", processingTime=" + processingTime +
                '}';
    }
}
