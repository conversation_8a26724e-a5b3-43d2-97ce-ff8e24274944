package com.ruoyi.baidu.websocket;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.net.URI;
import java.nio.ByteBuffer;
import org.springframework.stereotype.Component;

@Component
public class SocketResultUtils {

    private static final Logger log = LoggerFactory.getLogger(SocketResultUtils.class);

    public static WebSocketClient getClient(String uri) {
        try {
            log.info("创建WebSocket客户端连接到: {}", uri);
            return new WebSocketClient(new URI(uri)) {
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    log.info("已连接到百度语音识别服务: {}", uri);
                }

                @Override
                public void onMessage(String message) {
                    log.info("从百度服务收到文本消息: {}", message);
                    // 将百度服务的消息转发回前端
                    for (SocketServerUtils client : SocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendText(message);
                        } catch (Exception e) {
                            log.error("转发文本消息到客户端失败", e);
                        }
                    }
                }

                @Override
                public void onMessage(ByteBuffer bytes) {
                    log.info("从百度服务收到二进制消息，长度: {}", bytes.remaining());
                    // 将百度服务的二进制消息转发回前端
                    for (SocketServerUtils client : SocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendBinary(bytes);
                        } catch (Exception e) {
                            log.error("转发二进制消息到客户端失败", e);
                        }
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("与百度语音识别服务的连接已关闭: code={}, reason={}, remote={}", code, reason, remote);
                    // 通知前端连接已关闭
                    for (SocketServerUtils client : SocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendText("{\"type\":\"CLOSE\",\"code\":" + code + ",\"reason\":\"" + reason + "\"}");
                        } catch (Exception e) {
                            log.error("发送关闭通知到客户端失败", e);
                        }
                    }
                }

                @Override
                public void onError(Exception ex) {
                    log.error("与百度语音识别服务的连接发生错误: {}", ex.getMessage(), ex);
                    // 通知前端发生错误
                    for (SocketServerUtils client : SocketServerUtils.clients) {
                        try {
                            client.session.getAsyncRemote().sendText("{\"type\":\"ERROR\",\"message\":\"" + ex.getMessage() + "\"}");
                        } catch (Exception e) {
                            log.error("发送错误通知到客户端失败", e);
                        }
                    }
                }
            };
        } catch (Exception e) {
            log.error("创建WebSocket客户端失败", e);
            return null;
        }
    }
}
