package com.ruoyi.baidu.dto;

import lombok.Data;

@Data
public class SpeechSynthesisDto {

    /**
     * 文本
     */
    private String content;

    /**
     * 语速
     */
    private Integer speed;

    /**
     * 音调
     */
    private Integer volume;

    /**
     * 音量
     */
    private Integer pitch;

    private Integer per;

    public SpeechSynthesisDto() {
    }

    public SpeechSynthesisDto(String content, Integer speed, Integer volume, Integer pitch) {
        this.content = content;
        this.speed = speed;
        this.volume = volume;
        this.pitch = pitch;
    }
}
