package com.ruoyi.baidu.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class DBSpeechSynthesisDto {

    /**
     * 文本数据
     */
    @NotBlank(message = "文本数据不能为空")
    private String content;
    /**
     * 发言人
     */
    @NotBlank(message = "发言人不能为空")
    private String voiceType;
    /**
     * 编码格式 wav / pcm / ogg_opus / mp3，默认为 pcm
     * 注意：wav 不支持流式
     */
    private String encoding;
    /**
     * 语速，取值范围：[0.8,2]，默认为 1，通常保留一位小数即可
     */
    private Double speedRatio;

    public DBSpeechSynthesisDto() {
    }

    public DBSpeechSynthesisDto(String content, String voiceType, String encoding, Double speedRatio) {
        this.content = content;
        this.voiceType = voiceType;
        this.encoding = encoding;
        this.speedRatio = speedRatio;
    }
}
