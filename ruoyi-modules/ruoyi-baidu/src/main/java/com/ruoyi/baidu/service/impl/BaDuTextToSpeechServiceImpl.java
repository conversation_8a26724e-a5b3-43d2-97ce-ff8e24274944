package com.ruoyi.baidu.service.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.baidu.dto.DBSpeechSynthesisDto;
import com.ruoyi.baidu.dto.SpeechSynthesisDto;
import com.ruoyi.baidu.dto.TtsRequest;
import com.ruoyi.baidu.service.IBaDuTextToSpeechService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import okhttp3.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.net.URLEncoder;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


@Service
public class BaDuTextToSpeechServiceImpl implements IBaDuTextToSpeechService {

    // token访问路径
    private static final String serverURL = "https://aip.baidubce.com/oauth/2.0/token";
    // 语音合成token
    private static String token = "";

    // 语音识别token
    private static String ASRToken = "";
    //你的apikey
    @Value("${speed.clientId}")
    private String client_id;
    //你的secretKey
    @Value("${speed.clientSecret}")
    private String client_secret;

    //你的apikey
    @Value("${voice.clientId}")
    private String VoiceClient_id;
    //你的secretKey
    @Value("${voice.clientSecret}")
    private String VoiceClient_secret;

    @Value("${doubao.douBaoId}")
    private String douBaoId;

    @Value("${doubao.douBaoToken}")
    private String douBaoToken;

    @Value("${ue.textLength}")
    private Integer textLength;

    @Value("${ue.filePathWin}")
    private String filePathWin;

    @Value("${ue.filePathLinux}")
    private String filePathLinux;

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    private static final Logger log = LoggerFactory.getLogger(BaDuTextToSpeechServiceImpl.class);


    @Override
    public AjaxResult getBaDuApiToken() {
        AjaxResult ajaxResult = new AjaxResult();
        String token = getToken();
        if (token != null && !token.equals("")){
            ajaxResult.put("token",token);
            ajaxResult.put("code",200);
        }
        return ajaxResult;
    }

    @Override
    public AjaxResult getBaDuASRToken() {
        AjaxResult ajaxResult = new AjaxResult();
        String token = getASRToken();
        if (token != null && !token.equals("")){
            ajaxResult.put("token",token);
            ajaxResult.put("code",200);
        }
        return ajaxResult;
    }

    @Override
    public byte[] YRTTS(SpeechSynthesisDto speechSynthesisDto) {
        // 校验参数


        String content = speechSynthesisDto.getContent();
        Integer per = speechSynthesisDto.getPer();
        Integer speed = speechSynthesisDto.getSpeed();
        Integer pitch = speechSynthesisDto.getPitch();
        Integer volume = speechSynthesisDto.getVolume();
        if (content == null || content.length() > 60) {
            throw new IllegalArgumentException("文本内容不能为空且长度不得超过60个字符");
        }
        if ((speed != null && (speed < 0 || speed > 15)) ||
                (pitch != null && (pitch < 0 || pitch > 15)) ||
                (volume != null && (volume < 0 || volume > 15))) {
            throw new IllegalArgumentException("speed、pitch、volume的取值范围必须在0-15之间");
        }
        // 获取token
        String token = getToken(client_id, client_secret);
        System.out.println("token:" + token);
        try {
            // 对文本进行两次 URL 编码
            String encodedText = URLEncoder.encode(URLEncoder.encode(content, "UTF-8"), "UTF-8");
            // 构建请求体，添加 tex 参数
            String requestBody = "tok=" + token
                    + "&tex=" + encodedText
                    + "&cuid=RR1m6cJIU7zhmZb7l3u5VWYhAjOG3nEe"
                    + "&ctp=1"
                    + "&lan=zh"
                    + "&spd=" + (speechSynthesisDto.getSpeed() != null ? speechSynthesisDto.getSpeed() : "5")
                    + "&pit=" + (speechSynthesisDto.getPitch() != null ? speechSynthesisDto.getPitch() : "5")
                    + "&vol=" + (speechSynthesisDto.getVolume() != null ? speechSynthesisDto.getVolume() : "5")
                    + "&per=" + (speechSynthesisDto.getPer() != null ? speechSynthesisDto.getPer() : "1")
                    + "&aue=3";

            MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
            RequestBody body = RequestBody.create(mediaType, requestBody);
            Request request = new Request.Builder()
                    .url("https://tsn.baidu.com/text2audio")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "*/*")
                    .build();

            Response response = HTTP_CLIENT.newCall(request).execute();
            // 检查响应的 Content-Type 来确定是否合成成功
            String contentType = response.header("Content-Type");
            if (contentType != null && contentType.startsWith("audio")) {
                // 合成成功，返回音频数据
                return response.body().bytes();
                // 可以将音频数据保存为文件或返回 Base64 编码的字符串
                // return Base64.getEncoder().encodeToString(audioData);
            } else {
                // 合成失败，记录错误信息
                String errorResponse = response.body().string();
                System.err.println("语音合成失败: " + errorResponse);
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String DBTTS(DBSpeechSynthesisDto dbSpeechSynthesisDto) {
        // 文本
        String content = dbSpeechSynthesisDto.getContent();
        // 发言人
        String voiceType = dbSpeechSynthesisDto.getVoiceType();
        // 编码格式
        // String encoding = dbSpeechSynthesisDto.getEncoding() != null ? dbSpeechSynthesisDto.getEncoding() : "mp3";
        // 语速
        Double speedRatio = dbSpeechSynthesisDto.getSpeedRatio() != null ? dbSpeechSynthesisDto.getSpeedRatio() : 1.0;
        try {
            // 豆包语音合成API地址
            String apiUrl = "https://openspeech.bytedance.com/api/v1/tts";
            // 设置appid和access_token（从配置中获取）
            String appid = douBaoId;
            String accessToken = douBaoToken;
            // 构建请求对象
            TtsRequest ttsRequest = TtsRequest.builder()
                    .app(TtsRequest.App.builder()
                        .appid(appid)
                        .cluster("volcano_tts")
                        .build())
                    .user(TtsRequest.User.builder()
                        .uid("user_" + System.currentTimeMillis())
                        .build())
                    .audio(TtsRequest.Audio.builder()
                        .encoding("mp3")
                        .voiceType(voiceType)
                        .speedRatio(speedRatio)
                        .build())
                    .request(TtsRequest.Request.builder()
                        .reqID(UUID.randomUUID().toString())
                        .operation("query")
                        .text(content)
                        .build())
                    .build();
                // 转换为JSON字符串
                String reqBody = JSON.toJSONString(ttsRequest);
                // 创建OkHttp客户端
                OkHttpClient client = new OkHttpClient();
                // 创建请求体
                RequestBody body = RequestBody.create(reqBody,MediaType.parse("application/json; charset=utf-8"));

                // 构建HTTP请求
                Request request = new Request.Builder()
                        .url(apiUrl)
                        .header("Authorization", "Bearer;" + accessToken)
                        .post(body)
                        .build();
                // 执行请求
                Response response = client.newCall(request).execute();
            // 解析响应
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            String data = jsonObject.getString("data");
            return data;
        } catch (Exception e) {
            throw new RuntimeException("网络不佳请稍后再试");
        }
    }

    /**
     * ue端 豆包文本转语音输出音频文件
     * @param dbSpeechSynthesisDto
     * @return 包含文件路径和文件名的Map
     */
    @Override
    public Map<String, Object> DBtextToSpeechFile(DBSpeechSynthesisDto dbSpeechSynthesisDto) {
        // 文本
        String content = dbSpeechSynthesisDto.getContent();
        // 发言人
        String voiceType = dbSpeechSynthesisDto.getVoiceType();
        // 编码格式
        String encoding = dbSpeechSynthesisDto.getEncoding() != null ? dbSpeechSynthesisDto.getEncoding() : "wav";
        // 语速
        Double speedRatio = dbSpeechSynthesisDto.getSpeedRatio() != null ? dbSpeechSynthesisDto.getSpeedRatio() : 1.0;
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 豆包语音合成API地址
            String apiUrl = "https://openspeech.bytedance.com/api/v1/tts";
            // 设置appid和access_token（从配置中获取）
            String appid = douBaoId;
            String accessToken = douBaoToken;
            // 创建固定的保存目录
            String saveDirectory;
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                saveDirectory = filePathWin;
            } else {
                saveDirectory = filePathLinux;
            }

            // 创建文件名（使用时间戳确保唯一性）
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = "speech_" + timestamp + "." + encoding;
            String filePath = saveDirectory + fileName;

            // 确保目录存在
            java.io.File directory = new java.io.File(saveDirectory);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 构建请求对象
            TtsRequest ttsRequest = TtsRequest.builder()
                    .app(TtsRequest.App.builder()
                            .appid(appid).cluster("volcano_tts").build())
                    .user(TtsRequest.User.builder()
                            .uid("user_" + System.currentTimeMillis()).build())
                    .audio(TtsRequest.Audio.builder()
                            .encoding(encoding).voiceType(voiceType).speedRatio(speedRatio).build())
                    .request(TtsRequest.Request.builder()
                            .reqID(UUID.randomUUID().toString()).operation("query").text(content).build())
                    .build();

            // 转换为JSON字符串
            String reqBody = JSON.toJSONString(ttsRequest);

            // 创建OkHttp客户端
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build();

            // 创建请求体
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), reqBody);

            // 构建HTTP请求
            Request request = new Request.Builder()
                    .url(apiUrl)
                    .header("Authorization", "Bearer;" + accessToken)
                    .post(body)
                    .build();

            // 记录请求开始时间
            long requestStartTime = System.currentTimeMillis();
            log.info("开始发送语音合成请求，文本长度: " + content.length() + " 字符");
            
            // 执行请求
            Response response = client.newCall(request).execute();
            
            // 计算请求执行时间
            long requestEndTime = System.currentTimeMillis();
            long requestDuration = requestEndTime - requestStartTime;
            log.info("语音合成请求执行完成，耗时: " + requestDuration + " 毫秒");

            // 解析响应
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            String base64Data = jsonObject.getString("data");

            // 将base64数据解码为字节数组
            byte[] audioBytes = Base64.getDecoder().decode(base64Data);

            // 创建输出流，用于写入音频数据
            java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath);
            fos.write(audioBytes);
            fos.close();

            // 计算总执行时间
            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;
            
            log.info("音频文件已保存至: " + filePath);
            log.info("总处理时间: " + totalDuration + " 毫秒，文本长度: " + content.length() + 
                    " 字符，音频大小: " + audioBytes.length + " 字节");

            // 构建包含文件路径、文件名和base64数据的Map
            Map<String, Object> result = new HashMap<>();
            result.put("file_path", saveDirectory);
            result.put("file_name", fileName);
            result.put("base64_audio", base64Data);

            // 返回Map
            return result;
        } catch (Exception e) {
            throw new RuntimeException("网络不佳或保存文件失败，请稍后再试: " + e.getMessage());
        }
    }

    @Override
    public String BaiDuASR(Map<String, String> option) {
        String ASRToken = getASRToken();
        String result = "";
        String speech = option.get("speech");
        String len = option.get("len");
        String dev_pid = option.get("dev_pid");
        String cuid = option.get("cuid");
        String jsonBody = "{"
                + "\"format\": \"wav\","
                + "\"rate\": 16000,"
                + "\"channel\": 1,"
                + "\"dev_pid\": " + dev_pid + ","
                + "\"cuid\": \"" + cuid + "\","
                + "\"token\": \"" + ASRToken + "\","
                + "\"speech\": \"" + speech + "\","
                + "\"len\": " + len
                + "}";

        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, jsonBody);
            Request request = new Request.Builder()
                    .url("https://vop.baidu.com/server_api")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .build();
            Response response = HTTP_CLIENT.newCall(request).execute();
            result = response.body().string();
//            System.out.println(response.body().string());
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        System.out.println(result);
        return result;
    }

    /**
     * 获取token公共方法
     * @param client_id
     * @param client_secret
     * @return
     */
    private String getToken(String client_id,String client_secret){
        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "");
            String requestUrl =serverURL + "?" + "client_id=" + client_id + "&client_secret=" + client_secret + "&grant_type=client_credentials";
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .build();
            Response response = HTTP_CLIENT.newCall(request).execute();
            String responseBodyString = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBodyString);
            token = (String) jsonObject.get("access_token");
        }catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }


    // =====================  后续优化掉 =====================
    /**
     * 获取语音合成token
     * @return
     */
    private String getToken(){
        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "");
            String requestUrl =serverURL + "?" + "client_id=" + client_id + "&client_secret=" + client_secret + "&grant_type=client_credentials";
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .build();
            Response response = HTTP_CLIENT.newCall(request).execute();
            String responseBodyString = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBodyString);
            token = (String) jsonObject.get("access_token");
        }catch (Exception e){
            e.printStackTrace();
        }
        return token;
    }

    /**
     * 获取语音识别token
     * @return
     */
    private String getASRToken(){
        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "");
            String requestUrl =serverURL + "?" + "client_id=" + VoiceClient_id + "&client_secret=" + VoiceClient_secret + "&grant_type=client_credentials";
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .build();
            Response response = HTTP_CLIENT.newCall(request).execute();
            String responseBodyString = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBodyString);
            ASRToken = (String) jsonObject.get("access_token");
        }catch (Exception e){
            e.printStackTrace();
        }
        return ASRToken;
    }
}
