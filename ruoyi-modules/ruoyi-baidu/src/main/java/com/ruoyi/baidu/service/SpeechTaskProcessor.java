package com.ruoyi.baidu.service;

import com.ruoyi.baidu.domain.SpeechTask;
import com.ruoyi.baidu.domain.TaskResult;
import com.ruoyi.baidu.utils.RedisSpeechSemaphore;
import com.ruoyi.baidu.utils.RedisSpeechTaskQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音合成任务处理服务
 * 从Redis队列获取任务并处理
 */
@Service
public class SpeechTaskProcessor {
    private static final Logger log = LoggerFactory.getLogger(SpeechTaskProcessor.class);

    // 工作线程数
    private static final int WORKER_THREADS = 10;

    @Autowired
    private RedisSpeechTaskQueue taskQueue;

    @Autowired
    private RedisSpeechSemaphore semaphore;

    @Autowired
    private IBaDuTextToSpeechService baiduService;

    private final ExecutorService executorService = Executors.newFixedThreadPool(WORKER_THREADS);
    private final AtomicBoolean running = new AtomicBoolean(true);

    /**
     * 初始化方法，启动工作线程
     */
    @PostConstruct
    public void init() {
        // 启动工作线程
        for (int i = 0; i < WORKER_THREADS; i++) {
            executorService.submit(this::processTasksFromQueue);
        }
        log.info("语音合成任务处理服务已启动，工作线程数: {}", WORKER_THREADS);
    }

    /**
     * 销毁方法，关闭工作线程
     */
    @PreDestroy
    public void shutdown() {
        running.set(false);
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("语音合成任务处理服务已关闭");
    }

    /**
     * 从队列处理任务的工作线程
     */
    private void processTasksFromQueue() {
        // 循环处理任务 线程不死的话一直循环
        while (running.get() && !Thread.currentThread().isInterrupted()) {
            try {
                // 从队列获取任务，最多等待5秒
                SpeechTask task = taskQueue.dequeueTask(5);
                if (task == null) {
                    continue;
                }

                // 记录开始处理时间
                long startTime = System.currentTimeMillis();

                // 获取分布式信号量
                String permitId = semaphore.tryAcquire();
                if (permitId == null) {
                    // 无法获取信号量，将任务重新放回队列
                    log.warn("无法获取信号量，任务ID: {} 将重新入队", task.getTaskId());
                    taskQueue.enqueueTask(task.getDto());
                    continue;
                }

                try {
                    // 处理任务
                    log.info("开始处理任务，ID: {}", task.getTaskId());
                    String audioBase64 = baiduService.DBTTS(task.getDto());

                    // 创建成功结果
                    TaskResult result = TaskResult.success(task.getTaskId(), audioBase64, startTime);

                    // 保存处理结果
                    taskQueue.saveTaskResult(result);
                    log.info("任务处理成功，ID: {}, 耗时: {}ms", task.getTaskId(), result.getProcessingTime());
                } catch (Exception e) {
                    log.error("任务处理失败，ID: " + task.getTaskId(), e);
                    // 创建失败结果
                    TaskResult result = TaskResult.failure(task.getTaskId(), e.getMessage(), startTime);
                    taskQueue.saveTaskResult(result);
                } finally {
                    // 释放信号量
                    semaphore.release(permitId);
                }
            } catch (Exception e) {
                log.error("处理任务队列时发生错误", e);
                // 短暂休眠，避免在错误情况下CPU使用率过高
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    /**
     * 定时任务：监控队列状态
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorQueueStatus() {
        try {
            int availablePermits = semaphore.availablePermits();
            log.info("队列监控 - 可用信号量: {}", availablePermits);
        } catch (Exception e) {
            log.error("监控队列状态时发生错误", e);
        }
    }
}
