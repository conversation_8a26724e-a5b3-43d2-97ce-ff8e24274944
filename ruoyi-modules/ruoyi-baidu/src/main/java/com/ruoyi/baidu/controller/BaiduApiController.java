package com.ruoyi.baidu.controller;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.model.knowledgebase.Document;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.baidu.service.IBaiduApiService;
import com.ruoyi.common.security.annotation.InnerAuth;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/baidu")
public class BaiduApiController {

    @Autowired
    private IBaiduApiService baiduApiService;


    /**
     * 创建数据集
     * @return 数据集版本ID
     */
    @InnerAuth
    @PostMapping("/createData")
    public String createData(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String groupName = baiduDto.getGroupName();
        return baiduApiService.createData( ak,sk,groupName);
    }

    /**
     * 将文件上传到BOS
     * @return
     */
    @InnerAuth
    @PostMapping("/putObjectSimple")
    public String[] putObjectSimple(@RequestBody BaiduDto baiduDto){
        List<String> filePathList = baiduDto.getFilePathList();
        List<String> busiIdList = baiduDto.getBusiIdList();
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String domainName = baiduDto.getDomainName();
        String bosBucketName = baiduDto.getBosBucketName();
        return baiduApiService.putObjectSimple(filePathList,busiIdList,ak,sk,domainName,bosBucketName);
    }

    /**
     * 导入数据集文件
     * @return 成功true 失败false
     */
    @InnerAuth
    @PostMapping("/importDataFile")
    public Boolean importDataFile(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String datasetId = baiduDto.getDatasetId();
        String[] urlArray = baiduDto.getUrlArray();
        return baiduApiService.importDataFile( ak,sk,datasetId,urlArray);
    }

    /**
     * 查看数据集导入状态
     * @return 数据集导入状态
     */
    @InnerAuth
    @PostMapping("/importStatus")
    public int importStatus(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String datasetId = baiduDto.getDatasetId();
        return baiduApiService.importStatus( ak,sk,datasetId);
    }

    /**
     * 发布数据集
     * @return 成功true 失败false
     */
    @InnerAuth
    @PostMapping("/releaseDataSet")
    public Boolean releaseDataSet(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String datasetId = baiduDto.getDatasetId();
        return baiduApiService.releaseDataSet( ak,sk,datasetId);
    }

    /**
     * 删除BOS桶中文件夹或文件
     * @return
     */
    @InnerAuth
    @PostMapping("/delbos")
    public boolean delbos(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        List<String> filePathList = baiduDto.getFilePathList();
        List<String> busiIdList = baiduDto.getBusiIdList();
        boolean flag = baiduDto.getFlag();
        String bosBucketName = baiduDto.getBosBucketName();
        return baiduApiService.delbos(filePathList,busiIdList,ak,sk,flag,bosBucketName);
    }

    /**
     * 删除数据集
     * @return 成功true 失败false
     */
    @InnerAuth
    @PostMapping("/deldata")
    public Boolean deldata(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String datasetId = baiduDto.getDatasetId();
        return baiduApiService.deldata( ak,sk,datasetId);
    }


    /**
     * 创建模型精调任务
     * @return 任务ID
     */
    @InnerAuth
    @PostMapping("/modelFineTuningTask")
    public String modelFineTuningTask(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        Map<String, String> map = baiduDto.getMap();
        return baiduApiService.modelFineTuningTask(ak,sk,map);
    }

    /**
     * 查询训练任务执行状态
     * @return 任务运行状态
     */
    @InnerAuth
    @PostMapping("/fineTuningTaskDetails")
    public String fineTuningTaskDetails(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String taskId = baiduDto.getTaskId();
        return baiduApiService.fineTuningTaskDetails(ak,sk,taskId);
    }

    /**
     * 新建模型版本
     * @return 模型版本id
     */
    @InnerAuth
    @PostMapping("/newModelVersion")
    public String newModelVersion(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String modelSetId = baiduDto.getModelSetId();
        String taskId = baiduDto.getTaskId();
        return baiduApiService.newModelVersion(ak,sk,modelSetId,taskId);
    }

    /**
     * 创建知识库-旧（Open API）
     * @return 知识库id
     */
    @InnerAuth
    @PostMapping("/creatdataSet")
    public String creatdataSet(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String name = baiduDto.getName();
        return baiduApiService.creatdataSet(secretkey,name);
    }
    /**
     * 创建知识库-新
     * @return 知识库id（Open API）
     */
    @InnerAuth
    @PostMapping("/creatdataSetNew")
    public String creatdataSetNew(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String name = baiduDto.getName();
        String dtoDescription = baiduDto.getDescription();
        //如果有携带知识库的目录信息
        if(baiduDto.getPathPrefix()!=null){
            String PathPrefix = baiduDto.getPathPrefix();
            return baiduApiService.creatdataSetNew(secretkey,name,dtoDescription,PathPrefix);
        }
        return baiduApiService.creatdataSetNew(secretkey,name,dtoDescription);
    }

    /**
     * 删除知识库
     */
    @InnerAuth
    @PostMapping("/delKnowledgeBase")
    public void delKnowledgeBase(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        baiduApiService.delKnowledgeBase(secretkey,datasetId);
    }

    /**
     * 导入文件到知识库
     * @return 文件id数组
     */
    @InnerAuth
    @PostMapping("/importFile")
    public String[] importFile(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        List<String> filePath = baiduDto.getFilePath();
        boolean enhanced = baiduDto.getEnhanced();
        return baiduApiService.importFile(secretkey,datasetId,filePath,enhanced);
    }

    /**
     * 导入文件到知识库
     * @return 文件id数组
     */
    @InnerAuth
    @PostMapping("/importFileNew")
    public String[] importFileNew(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        List<String> filePath = baiduDto.getFilePath();
        boolean enhanced = baiduDto.getEnhanced();
        String contentFormat = baiduDto.getContentFormat();
        return baiduApiService.importFileNew(secretkey,datasetId,filePath,enhanced,contentFormat);
    }

    @InnerAuth
    @PostMapping("/getDocuments")
    public Document[] getDocuments(@RequestBody BaiduDto baiduDto) throws IOException, AppBuilderServerException {
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        String after=baiduDto.getAfter();
        String before=baiduDto.getBefore();
        return baiduApiService.getDocuments(secretkey,datasetId,after,before);
    }

    /**
     * 导入文件到知识库
     * @return 文件id数组
     */
    @InnerAuth
    @PostMapping("/delKnowledgeBaseFile")
    public boolean delKnowledgeBaseFile(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        String fileId = baiduDto.getFileId();
        return baiduApiService.delKnowledgeBaseFile(secretkey,datasetId,fileId);
    }


    /**
     * 删除知识库文件
     * @return 成功失败
     */
    @InnerAuth
    @PostMapping("/delDataSetFile")
    public boolean delDataSetFile(@RequestBody BaiduDto baiduDto){
        String secretkey = baiduDto.getSecretkey();
        String datasetId = baiduDto.getDatasetId();
        String fileId = baiduDto.getFileId();
        return baiduApiService.delDataSetFile(secretkey,datasetId,fileId);
    }

    /**
     * 对话大模型
     * @param baiduDto
     * @return 模型返回内容
     */
    @InnerAuth
    @PostMapping("/sendContent")
    public String sendContent(@RequestBody BaiduDto baiduDto) throws IOException, JSONException {
        String messages = baiduDto.getMessages();
        String apiKey = baiduDto.getApiKey();
        String apiURL = baiduDto.getApiUrl();
        String secretKey = baiduDto.getSecretkey();
        return baiduApiService.sendContent(messages,apiKey,secretKey,apiURL);
    }

    /**
     * 对话知识库
     * @return 知识库返回内容
     */
    @InnerAuth
    @PostMapping("/knowledgeBase")
    public String knowledgeBase(@RequestBody BaiduDto baiduDto) throws IOException, AppBuilderServerException {
        String query = baiduDto.getQuery();
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        return baiduApiService.knowledgeBase(query,appid,secretkey);
    }

    /**
     * 创建prompt优化任务
     * @return prompt优化任务id
     */
    @InnerAuth
    @PostMapping("/insertPrompt")
    public String insertPromptOptimization(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String jsonBody = baiduDto.getJsonBody();
        return baiduApiService.insertPromptOptimization(ak,sk,jsonBody);
    }

    /**
     * 获取prompt优化任务详情
     * @return
     */
    @InnerAuth
    @PostMapping("/getPromptInfo")
    public com.alibaba.fastjson2.JSONObject getPromptInfo(@RequestBody BaiduDto baiduDto){
        String ak = baiduDto.getAk();
        String sk = baiduDto.getSk();
        String optimizationId = baiduDto.getOptimizationId();
        return baiduApiService.getPromptInfo(ak,sk,optimizationId);
    }

    /**
     * 论文研读-新建会话
     * @return
     */
    @InnerAuth
    @PostMapping("/newSession")
    public String newSession(@RequestBody BaiduDto baiduDto){
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        return baiduApiService.newSession(appid,secretkey);
    }
    /**
     * 论文研读-上传文件
     * @return
     */
    @InnerAuth
    @PostMapping("/getfileId")
    public String getfileId(@RequestBody BaiduDto baiduDto) throws IOException, JSONException {
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        String conversationId = baiduDto.getConversationId();
        File file = baiduDto.getFile();
        return baiduApiService.getfileId(appid,secretkey,conversationId,file);
    }
    /**
     * 论文研读-获取回答
     * @return
     */
    @InnerAuth
    @PostMapping("/getAnswer")
    public String getAnswer(@RequestBody BaiduDto baiduDto) throws IOException, JSONException {
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        String conversationId = baiduDto.getConversationId();
        String query= baiduDto.getQuery();
        String fileId = baiduDto.getFileId();
        String answer = baiduApiService.getAnswer(appid, secretkey, conversationId, query, fileId);
        return answer;
    }
    /**
     * 文生图-获取图片
     * @return
     */
   // @InnerAuth
    @PostMapping("/getImage")
    public Map<String,String> getImage(@RequestBody BaiduDto baiduDto) throws IOException,AppBuilderServerException {
        String query = baiduDto.getQuery();
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        String conversationId = baiduDto.getConversationId();
        String basePath = baiduDto.getBasePath();
        Map<String, String> image = null;

        if (conversationId!=null){
          image = baiduApiService.getImage(query, appid, secretkey, conversationId,basePath);
        }else {
            baiduApiService.getImage(query, appid, secretkey, null,basePath);
        }
        return image;
    }

    /**
     * 获取图片信息
     * @param baiduDto
     * @return
     * @throws IOException
     * @throws JSONException
     */
    @InnerAuth
    @PostMapping("/getImageInfo")
    public String getImageInfo(@RequestBody BaiduDto baiduDto) throws Exception{
        String appid = baiduDto.getAppid();
        String secretkey = baiduDto.getSecretkey();
        String query= baiduDto.getQuery();
        List<String> filePath = baiduDto.getFilePath();
        return baiduApiService.getImageInfo(appid,secretkey,filePath,query);
//        return "success";
    }
}
