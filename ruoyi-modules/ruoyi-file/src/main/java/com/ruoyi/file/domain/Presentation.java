package com.ruoyi.file.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 老师课程对象 s_presentation
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Presentation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 学校 */
    @Excel(name = "学校")
    private String school;

    /** 学院 */
    @Excel(name = "学院")
    private String college;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 年级 */
    @Excel(name = "年级")
    private String grade;

    /** 课程 */
    @Excel(name = "课程")
    private String course;

    /** 课件名称 */
    @Excel(name = "课件名称")
    private String presentationName;

    /** 课件名称 */
    @Excel(name = "课件总页数")
    private Long presentationAllpage;


    /** 课件id */
    @Excel(name = "课件id")
    private Long presentationId;

    /** 发布状态 */
    @Excel(name = "发布状态")
    private String presentationStatus;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createUser;

    /** 讲演稿路径 */
    @Excel(name = "讲演稿路径")
    private String speechdraftpath;

    /** 课件未拆分的路径 */
    @Excel(name = "课件未拆分的路径")
    private String presentationPath;

    /** 未拆分相对的路径 */
    @Excel(name = "课件未拆分相对的路径")
    private String presentationHttp;


    @Excel(name = "课件文件id")
    private String presentationFileId;


    @Excel(name = "讲演稿文件id")
    private String speechdraftFileId;

    @Excel(name = "讲演稿文本")
    private String speechdraftFileTxt;



    @Excel(name = "所属单位")
    @TableField(exist = false)
    private Long[] affiliatedUnit;

    @Excel(name = "所属单位名称")
    @TableField(exist = false)
    private String[] affiliatedUnitName;

    /** 是否联盟课 */
    private String isAllianceCourse;

    @Excel(name = "审核状态0未审核1通过2不通过")
    private int isExamine;

    @Excel(name = "审核建议")
    @TableField(exist = false)
    private String suggestion;

    @Excel(name = "当前返回数据是不是教务处获取的")
    @TableField(exist = false)
    private int authority;

    /** 章节 */
    @Excel(name = "章节")
    private String chapter;

    @Excel(name = "教材id")
    private String textBookId;

    /** ppt中带有视频的页面索引str*/
    @TableField(exist = false)
    private String videoPageIndexes;


    public String getVideoPageIndexes() {
        return videoPageIndexes;
    }

    public void setVideoPageIndexes(String videoPageIndexes) {
        this.videoPageIndexes = videoPageIndexes;
    }
    public String getTextBookId() {
        return textBookId;
    }

    public void setTextBookId(String textBookId) {
        this.textBookId = textBookId;
    }

    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }

    public int getAuthority() {
        return authority;
    }

    public void setAuthority(int authority) {
        this.authority = authority;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public int getIsExamine() {
        return isExamine;
    }

    public void setIsExamine(int isExamine) {
        this.isExamine = isExamine;
    }

    public String getSpeechdraftFileTxt() {
        return speechdraftFileTxt;
    }

    public void setSpeechdraftFileTxt(String speechdraftFileTxt) {
        this.speechdraftFileTxt = speechdraftFileTxt;
    }

    public Long[] getAffiliatedUnit() {
        return affiliatedUnit;
    }

    public void setAffiliatedUnit(Long[] affiliatedUnit) {
        this.affiliatedUnit = affiliatedUnit;
    }

    public String[] getAffiliatedUnitName() {
        return affiliatedUnitName;
    }

    public void setAffiliatedUnitName(String[] affiliatedUnitName) {
        this.affiliatedUnitName = affiliatedUnitName;
    }

    public String getPresentationFileId() {
        return presentationFileId;
    }

    public void setPresentationFileId(String presentationFileId) {
        this.presentationFileId = presentationFileId;
    }

    public String getSpeechdraftFileId() {
        return speechdraftFileId;
    }

    public void setSpeechdraftFileId(String speechdraftFileId) {
        this.speechdraftFileId = speechdraftFileId;
    }

    public String getPresentationHttp() {
        return presentationHttp;
    }

    public void setPresentationHttp(String presentationHttp) {
        this.presentationHttp = presentationHttp;
    }

    public Long getPresentationAllpage() {
        return presentationAllpage;
    }

    public void setPresentationAllpage(Long presentationAllpage) {
        this.presentationAllpage = presentationAllpage;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }




    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSchool(String school)
    {
        this.school = school;
    }

    public String getSchool()
    {
        return school;
    }
    public void setCollege(String college)
    {
        this.college = college;
    }

    public String getCollege()
    {
        return college;
    }
    public void setMajor(String major)
    {
        this.major = major;
    }

    public String getMajor()
    {
        return major;
    }
    public void setCourse(String course)
    {
        this.course = course;
    }

    public String getCourse()
    {
        return course;
    }
    public void setPresentationName(String presentationName)
    {
        this.presentationName = presentationName;
    }

    public String getPresentationName()
    {
        return presentationName;
    }
    public void setPresentationId(Long presentationId)
    {
        this.presentationId = presentationId;
    }

    public Long getPresentationId()
    {
        return presentationId;
    }
    public void setPresentationStatus(String presentationStatus)
    {
        this.presentationStatus = presentationStatus;
    }

    public String getPresentationStatus()
    {
        return presentationStatus;
    }
    public void setCreateUser(String createUser)
    {
        this.createUser = createUser;
    }

    public String getCreateUser()
    {
        return createUser;
    }
    public void setSpeechdraftpath(String speechdraftpath)
    {
        this.speechdraftpath = speechdraftpath;
    }

    public String getSpeechdraftpath()
    {
        return speechdraftpath;
    }
    public void setPresentationPath(String presentationPath)
    {
        this.presentationPath = presentationPath;
    }

    public String getPresentationPath()
    {
        return presentationPath;
    }

    public String getIsAllianceCourse() {
        return isAllianceCourse;
    }

    public void setIsAllianceCourse(String isAllianceCourse) {
        this.isAllianceCourse = isAllianceCourse;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("school", getSchool())
                .append("college", getCollege())
                .append("major", getMajor())
                .append("course", getCourse())
                .append("grade", getGrade())
                .append("presentationName", getPresentationName())
                .append("presentationId", getPresentationId())
                .append("presentationStatus", getPresentationStatus())
                .append("createUser", getCreateUser())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("speechdraftpath", getSpeechdraftpath())
                .append("presentationPath", getPresentationPath())
                .toString();
    }
}
