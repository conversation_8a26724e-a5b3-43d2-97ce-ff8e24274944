package com.ruoyi.file.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.SystemUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.file.constant.Constants;
import com.ruoyi.file.constant.SymbolConstant;
import com.ruoyi.file.domain.*;
import com.ruoyi.file.exception.CustomException;
import com.ruoyi.file.mapper.SysFileInfoMapper;
import com.ruoyi.file.service.ISysFileInfoService;
import com.ruoyi.file.utils.FileCustomUtils;
import com.ruoyi.file.utils.ImageCompressUtil;
import com.ruoyi.system.api.dto.FileDeletionParamsDto;
import com.ruoyi.system.api.dto.TextFileCreationDto;
import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xslf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.openxmlformats.schemas.drawingml.x2006.main.CTRegularTextRun;
import org.openxmlformats.schemas.drawingml.x2006.main.CTSolidColorFillProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextCharacterProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.impl.CTTextFieldImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.swagger.readers.operation.SwaggerOperationTagsReader;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.file.utils.PptUtils.*;

/**
 * 文件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
@Service
@Slf4j
public class SysFileInfoServiceImpl extends ServiceImpl<SysFileInfoMapper, SysFileInfo> implements ISysFileInfoService {
    @Resource
    private SysFileInfoMapper sysFileInfoMapper;

    @Autowired
    private FileConfig fileConfig;
    @Resource
    private FileCustomUtils fileCustomUtils;


    @Value("${file.path.file-path-win}")
    String localFilePathWin;

    @Value("${file.path.filePathlinux}")
    String localFilePathLinux;

    @Value("${file.temp}")
    private String temp;
    @Autowired
    private SwaggerOperationTagsReader swaggerOperationTagsReader;

    /**
     * 查询文件信息
     *
     * @param id 文件信息主键
     * @return 文件信息
     */
    @Override
    public SysFileInfo selectSysFileInfoById(Long id) {
        return baseMapper.selectSysFileInfoById(id);
    }

    /**
     * 查询文件信息列表
     *
     * @param sysFileInfo 文件信息
     * @return 文件信息
     */
    @Override
    public List<SysFileInfo> selectSysFileInfoList(SysFileInfo sysFileInfo) {
        return baseMapper.selectSysFileInfoList(sysFileInfo);
    }

    /**
     * 新增文件信息
     *
     * @param sysFileInfo 文件信息
     * @return 结果
     */
    @Override
    public boolean insertSysFileInfo(SysFileInfo sysFileInfo) {
        sysFileInfo.setCreateTime(DateUtils.getNowDate());
        return save(sysFileInfo);
    }

    /**
     * 修改文件信息
     *
     * @param sysFileInfo 文件信息
     * @return 结果
     */
    @Override
    public boolean updateSysFileInfo(SysFileInfo sysFileInfo) {
        sysFileInfo.setUpdateTime(DateUtils.getNowDate());

        return updateById(sysFileInfo);
    }

    /**
     * 批量删除文件信息
     *
     * @param ids 需要删除的文件信息主键
     * @return 结果
     */
    @Override
    public int deleteSysFileInfoByIds(Long[] ids) {
        return baseMapper.deleteSysFileInfoByIds(ids);
    }

    /**
     * 删除文件信息信息
     *
     * @param id 文件信息主键
     * @return 结果
     */
    @Override
    public int deleteSysFileInfoById(Long id) {
        int re = 0;
        SysFileInfo fileInfo = sysFileInfoMapper.selectSysFileInfoById(id);
        if (fileInfo != null && fileInfo.getFilePath() != null) {
            File file = new File(fileInfo.getFilePath());
            boolean result = file.delete();
            System.out.println("==================删除id为" + id + "的文件," + result);
            re = baseMapper.deleteSysFileInfoById(id);
        }
        return re;


    }

    /**
     * 上传文件，返回文件的唯一标识
     *
     * @param file 要上传的文件
     * @return 文件id
     * <AUTHOR>
     * @date 2020/8/17 21:21
     */
    @Override
    public FileVo uploadFile(MultipartFile file, String modeltype) {
        Long fileId = queryUploadFileId();
        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();

        // 获取文件后缀
        String fileSuffix = null;

        if (StringUtils.isNotEmpty(originalFilename)) {
            fileSuffix = StrUtil.subAfter(originalFilename, SymbolConstant.PERIOD, true);
        }

        //大写转换为小写
        if (StringUtils.isNotBlank(fileSuffix)) {
            fileSuffix = fileSuffix.toLowerCase(Locale.ENGLISH);
        }

        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        //获取文件存储路径
        String filePath = getFilePath(fileSuffix, modeltype);

        //上传路径
        String uploadUrl = filePath + finalName;
        // 替换路径中的所有不一致的分隔符为 "/"
        uploadUrl = uploadUrl.replace("\\", "/").replace("//", "/");
        try {
            //图片处理
            byte[] bytes = file.getBytes();
            uploadUrl = fileUpload(bytes, uploadUrl);

            if (StringUtils.isNotBlank(uploadUrl)) {
                // 计算文件大小b
                long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(bytes.length), BigDecimal.valueOf(1))
                        .setScale(0, BigDecimal.ROUND_HALF_UP));
                if ("sfzh".equals(modeltype) && fileSizeKb >= 2 * 1024 * 1024) {
                    return null;
                }
                if ("provider".equals(modeltype) && fileSizeKb >= 20 * 1024 * 1024) {
                    return null;
                }
                // 存储文件信息
                SysFileInfo sysFileInfo = new SysFileInfo();
                sysFileInfo.setBusiModel(modeltype);
                sysFileInfo.setFileObjectName(String.valueOf(fileId));
                sysFileInfo.setFileOriginName(originalFilename);
                sysFileInfo.setFileSuffix(fileSuffix);
                sysFileInfo.setFileSizeKb(fileSizeKb);
                Path path = fileConfig.getPath();
                if (Objects.isNull(path)) {
                    throw new CustomException("操作失败，未获取到上传路径！");
                }
                if (SystemUtil.getOsInfo().isWindows()) {
                    String savePathWindows = path.getFilePathWin();
                    uploadUrl.replace(savePathWindows, "");
                }
                if (SystemUtil.getOsInfo().isLinux()) {
                    String savePathLinux = path.getFilePathLinux();
                    uploadUrl.replace(savePathLinux, "");
                }
                sysFileInfo.setFilePath(uploadUrl);
                sysFileInfo.setCreateTime(DateUtils.getNowDate());
                if (SecurityUtils.getUserId() != null) {
                    sysFileInfo.setCreateBy(SecurityUtils.getUserId());
                } else {
                    sysFileInfo.setCreateBy(10001L);
                }
                save(sysFileInfo);
                // 返回文件id

                return transferFileToVo(sysFileInfo, modeltype);
            }
            return null;
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * @param modeltype 文件
     * @param fileName  指定文件名
     * @description: 上传文件
     * @author: zhaoTianQi
     * @date: 2024/11/21 18:01
     * @param: file
     * @return: FileVo
     **/
    @Override
    public FileVo uploadFile(MultipartFile file, String modeltype, String fileName) {
        Long fileId = queryUploadFileId();
        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        // 找到最后一个点的位置
        int indexOfDot = originalFilename.lastIndexOf(".");
        originalFilename = fileName + originalFilename.substring(indexOfDot);

        // 获取文件后缀
        String fileSuffix = null;

        if (StringUtils.isNotEmpty(originalFilename)) {
            fileSuffix = StrUtil.subAfter(originalFilename, SymbolConstant.PERIOD, true);
        }

        //大写转换为小写
        if (StringUtils.isNotBlank(fileSuffix)) {
            fileSuffix = fileSuffix.toLowerCase(Locale.ENGLISH);
        }

        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        //获取文件存储路径
        String filePath = getFilePath(fileSuffix, modeltype);

        //上传路径
        String uploadUrl = filePath + finalName;
        // 替换路径中的所有不一致的分隔符为 "/"
        uploadUrl = uploadUrl.replace("\\", "/").replace("//", "/");
        try {
            //图片处理
            byte[] bytes = file.getBytes();
            uploadUrl = fileUpload(bytes, uploadUrl);

            if (StringUtils.isNotBlank(uploadUrl)) {
                // 计算文件大小b
                long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(bytes.length), BigDecimal.valueOf(1))
                        .setScale(0, BigDecimal.ROUND_HALF_UP));
                if ("sfzh".equals(modeltype) && fileSizeKb >= 2 * 1024 * 1024) {
                    return null;
                }
                if ("provider".equals(modeltype) && fileSizeKb >= 20 * 1024 * 1024) {
                    return null;
                }
                // 存储文件信息
                SysFileInfo sysFileInfo = new SysFileInfo();
                sysFileInfo.setBusiModel(modeltype);
                sysFileInfo.setFileObjectName(String.valueOf(fileId));
                sysFileInfo.setFileOriginName(originalFilename);
                sysFileInfo.setFileSuffix(fileSuffix);
                sysFileInfo.setFileSizeKb(fileSizeKb);
                Path path = fileConfig.getPath();
                if (Objects.isNull(path)) {
                    throw new CustomException("操作失败，未获取到上传路径！");
                }
                if (SystemUtil.getOsInfo().isWindows()) {
                    String savePathWindows = path.getFilePathWin();
                    uploadUrl.replace(savePathWindows, "");
                }
                if (SystemUtil.getOsInfo().isLinux()) {
                    String savePathLinux = path.getFilePathLinux();
                    uploadUrl.replace(savePathLinux, "");
                }
                sysFileInfo.setFilePath(uploadUrl);
                sysFileInfo.setCreateTime(DateUtils.getNowDate());
                if (SecurityUtils.getUserId() != null) {
                    sysFileInfo.setCreateBy(SecurityUtils.getUserId());
                } else {
                    sysFileInfo.setCreateBy(10001L);
                }
                save(sysFileInfo);
                // 返回文件id

				return transferFileToVo(sysFileInfo,modeltype);
			}
			return null;
		} catch (IOException e) {
			throw new CustomException(e.getMessage());
		}
	}

	@Override
	public FileVo uploadForTask(MultipartFile file, String modeltype, Long taskId) {
		Long fileId = queryUploadFileId();
		// 获取文件原始名称
		String originalFilename = file.getOriginalFilename();

        // 获取文件后缀
        String fileSuffix = null;

        if (StringUtils.isNotEmpty(originalFilename)) {
            fileSuffix = StrUtil.subAfter(originalFilename, SymbolConstant.PERIOD, true);
        }

        //大写转换为小写
        if (StringUtils.isNotBlank(fileSuffix)) {
            fileSuffix = fileSuffix.toLowerCase(Locale.ENGLISH);
        }

        // 生成文件的最终名称
        String finalName = fileId + SymbolConstant.PERIOD + fileSuffix;

        //获取文件存储路径
        String filePath = getFilePath(fileSuffix, modeltype);

        //上传路径
        String uploadUrl = filePath + finalName;
        try {
            //图片处理
            byte[] bytes = file.getBytes();
            uploadUrl = fileUpload(bytes, uploadUrl);

            if (StringUtils.isNotBlank(uploadUrl)) {
                // 计算文件大小b
                long fileSizeKb = Convert.toLong(NumberUtil.div(new BigDecimal(bytes.length), BigDecimal.valueOf(1))
                        .setScale(0, BigDecimal.ROUND_HALF_UP));
                // 存储文件信息
                SysFileInfo sysFileInfo = new SysFileInfo();
                sysFileInfo.setBusiModel(modeltype);
                sysFileInfo.setFileObjectName(String.valueOf(fileId));
                sysFileInfo.setFileOriginName(originalFilename);
                sysFileInfo.setFileSuffix(fileSuffix);
                sysFileInfo.setFileSizeKb(fileSizeKb);
                sysFileInfo.setBusiId(String.valueOf(taskId));
                Path path = fileConfig.getPath();
                if (Objects.isNull(path)) {
                    throw new CustomException("操作失败，未获取到上传路径！");
                }
                if (SystemUtil.getOsInfo().isWindows()) {
                    String savePathWindows = path.getFilePathWin();
                    uploadUrl.replace(savePathWindows, "");
                }
                if (SystemUtil.getOsInfo().isLinux()) {
                    String savePathLinux = path.getFilePathLinux();
                    uploadUrl.replace(savePathLinux, "");
                }
                sysFileInfo.setFilePath(uploadUrl);
                sysFileInfo.setCreateTime(DateUtils.getNowDate());
                if (SecurityUtils.getUserId() != null) {
                    sysFileInfo.setCreateBy(SecurityUtils.getUserId());
                } else {
                    sysFileInfo.setCreateBy(10001L);
                }
                save(sysFileInfo);
                // 返回文件id
                return transferFileToVo(sysFileInfo, modeltype);
            }
            return null;
        } catch (IOException e) {
            return null;
        }
    }

    @Override
    public void fileDownloadByFileObjectName(String fileObjectName, HttpServletRequest request, HttpServletResponse response) {
        SysFileInfo sysFileInfo = sysFileInfoMapper.selOneSysFileInfoByFileObjectName(fileObjectName);
        File file = new File(sysFileInfo.getFilePath());
        //设置响应的信息
        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(sysFileInfo.getFileOriginName(), "utf8"));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            //设置浏览器接受类型为流
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        try {
            //创建输入流读取文件
            InputStream inputStream = new FileInputStream(file);
            //获取response的输出流
            OutputStream outputStream = response.getOutputStream();
            int len;
            //一次传输1M大小字节
            byte[] bytes = new byte[1024];
            while ((len = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public int relationFile(Long[] ids, String busiId) {
        List<SysFileInfo> sysFileInfoList = sysFileInfoMapper.listByIds(Arrays.asList(ids));
        for (SysFileInfo fileInfo : sysFileInfoList) {
            fileInfo.setBusiId(busiId);
            sysFileInfoMapper.saveOrUpdateBatch(fileInfo);
        }
        return 1;
    }

    @Override
    public List<SysFileInfo> getFileInfo(String busiId) {

        return sysFileInfoMapper.getFileInfoListByWbryIds(busiId);

    }

    @Override
    public void deleteFile(String busiId) {
        //获取文件路径
        List<SysFileInfo> sysFileInfoList = sysFileInfoMapper.getFileInfoListByWbryIds(busiId);
        List<String> filePaths = sysFileInfoList.stream()
                .map(SysFileInfo::getFilePath)
                .collect(Collectors.toList());
        if (com.ruoyi.common.core.utils.StringUtils.isNotEmpty(filePaths.get(0))) {
            File file = new File(filePaths.get(0));
            // 检查文件是否存在
            if (file.exists()) {
                // 尝试删除文件
                boolean isDeleted = file.delete();

            }
        }
        sysFileInfoMapper.deleteSysFileInfoById(sysFileInfoList.get(0).getId());
    }

    @Override
    public SysFileInfo getFileInfoByFileId(String id) {
        return sysFileInfoMapper.selOneSysFileInfoByFileObjectName(id);
    }


    /**
     * 上传文件
     *
     * @param bytes     文件字节
     * @param uploadUrl 上传路径
     */
    private String fileUpload(byte[] bytes, String uploadUrl) {
        //存储文件
        FileUtil.writeBytes(bytes, uploadUrl);
        return uploadUrl;
    }

    /**
     * 把基础文件数据转换为vo类型
     *
     * @param sysFileInfo
     * @param modeltype
     **/
    private FileVo transferFileToVo(SysFileInfo sysFileInfo, String modeltype) {
        FileVo vo = new FileVo();
        /*vo.setId(sysFileInfo.getId());
         vo.setName(sysFileInfo.getFileObjectName());
        */
        vo.setId(Long.parseLong(sysFileInfo.getFileObjectName()));
        vo.setName(sysFileInfo.getFileOriginName());
        vo.setSize(sysFileInfo.getFileSizeKb());
        vo.setSubfix(sysFileInfo.getFileSuffix());
        vo.setPreview(sysFileInfo.getFilePath());

        return vo;
    }

    /**
     * 文件预览，目前只支持图片预览
     *
     * @param id       文件id
     * @param response 响应流
     */
    @Override
    public void preview(Long id, HttpServletResponse response) throws IOException {
        SysFileInfo sysFileInfo = selectSysFileInfoById(id);
        if (Objects.nonNull(sysFileInfo)) {
            Express express = fileConfig.getExpress();
            if (Objects.isNull(express)) {
                throw new CustomException("操作失败，未获取到配置项信息！");
            }
            if (StringUtils.isNotBlank(sysFileInfo.getFileSuffix()) && sysFileInfo.getFileSuffix().matches(express.getExpressType())) {
                String filePath = sysFileInfo.getFilePath();
                byte[] bytes = FileUtil.readBytes(filePath);

                //设置contentType
                response.setContentType(MediaType.IMAGE_JPEG_VALUE);

                //获取outputStream
                ServletOutputStream outputStream = response.getOutputStream();

                //输出
                IoUtil.write(outputStream, true, bytes);
            }

        }
    }

    @Override
    public void previewPath(String path, HttpServletResponse response) throws IOException {
        byte[] bytes = FileUtil.readBytes(path);
        //设置contentType
        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
        //获取outputStream
        ServletOutputStream outputStream = response.getOutputStream();
        //输出
        IoUtil.write(outputStream, true, bytes);
    }


    @Override
    public List<SysFileInfo> getFileInfoListByWbryIds(Long[] wbryIds) {
        LambdaQueryWrapper<SysFileInfo> qw = new LambdaQueryWrapper<>();
        qw.in(SysFileInfo::getBusiId, wbryIds);
        qw.eq(SysFileInfo::getBusiModel, "bmxy");
        qw.select(SysFileInfo::getFilePath, SysFileInfo::getFileOriginName);
        List<SysFileInfo> sysFileInfoList = this.list(qw);

        return sysFileInfoList;
    }


    /**
     * 单一文件下载
     *
     * @param id
     * @param request
     * @param response
     */
    @Override
    public void fileDownload(Long id, HttpServletRequest request, HttpServletResponse response) {
        SysFileInfo sysFileInfo = sysFileInfoMapper.selectSysFileInfoById(id);
        File file = new File(sysFileInfo.getFilePath());
        //设置响应的信息
        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(sysFileInfo.getFileOriginName(), "utf8"));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            //设置浏览器接受类型为流
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        try {
            //创建输入流读取文件
            InputStream inputStream = new FileInputStream(file);
            //获取response的输出流
            OutputStream outputStream = response.getOutputStream();
            int len;
            //一次传输1M大小字节
            byte[] bytes = new byte[1024];
            while ((len = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 如果开启图片压缩，则对图片进行压缩
     */
    private byte[] expressImage(MultipartFile file, String fileSuffix) throws IOException {

        if (StringUtils.isBlank(fileSuffix)) {
            return file.getBytes();
        }

        Express express = fileConfig.getExpress();
        if (Objects.isNull(express)) {
            throw new CustomException("操作失败，未获取到配置项信息！");
        }

        if (!express.isEnable()) {
            return file.getBytes();
        }

        if (StringUtils.isBlank(express.getExpressType())) {
            return file.getBytes();
        }

        if (fileSuffix.matches(express.getExpressType())) {
            ByteArrayOutputStream outputStream = ImageCompressUtil.ImageCompressToFile(file.getInputStream());
            if (Objects.nonNull(outputStream)) {
                return outputStream.toByteArray();
            }
        }

        return null;
    }

    /**
     * 文件存储路径 存储规则：根路径 + 模块 + 文件后缀类型 +用户id + 时间 （yyyyMMdd）
     *
     * @param fileSuffix 文件后缀
     * @param modeltype
     */
    private String getFilePath(String fileSuffix, String modeltype) {
        if (StringUtils.isBlank(fileSuffix)) {
            fileSuffix = Constants.FILE_DEFAULT_SUFFIX;
        }

        if (StringUtils.isBlank(modeltype)) {
            modeltype = Constants.FILE_DEFAULT_MODEL;
        }

        //文件根路径
        String root = "";
        Path path = fileConfig.getPath();
        if (Objects.isNull(path)) {
            throw new CustomException("操作失败，未获取到上传路径！");
        }
        if (SystemUtil.getOsInfo().isWindows()) {
            String savePathWindows = path.getFilePathWin();
            if (!FileUtil.exist(savePathWindows)) {
                FileUtil.mkdir(savePathWindows);
            }
            root = savePathWindows;
        } else {
            String savePathLinux = path.getFilePathLinux();
            if (!FileUtil.exist(savePathLinux)) {
                FileUtil.mkdir(savePathLinux);
            }
            root = savePathLinux;
        }


        String time = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate());
        long userId = SecurityUtils.getUserId();
        //相对文件路径
        return root + File.separatorChar + modeltype + File.separatorChar + fileSuffix + File.separatorChar + userId + File.separatorChar + time + File.separatorChar;
    }

    /**
     * 获取随机文件名称
     */
    public Long queryUploadFileId() {

        Date dt = new Date();
        Long lSysTime1 = dt.getTime() / 1000;
        String datestr = String.valueOf(lSysTime1);
        //得到秒数，Date类型的getTime()返回毫秒数
        String str = "0123456789";
        StringBuffer buff = new StringBuffer();
        buff.append(datestr);
        for (int i = 0; i < 6; i++) {
            char ch = str.charAt(new Random().nextInt(str.length()));
            buff.append(ch);
        }
        return Long.valueOf(buff.toString());

    }

    public String getCurrentTime() {
        String result = "";
        Calendar cal = Calendar.getInstance();
        int month = cal.get(Calendar.MONTH) + 1;
        int date = cal.get(Calendar.DATE) + 1;
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        result = month + "月" + date + "日" + " " + hour + "点" + minute + "分" + second + "秒";
        return result;
    }


    /**
     * @param unZip
     * @description: 解压文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 15:59
     * @param: unZip
     */
    @Override
    public Boolean extractZipFile(UnZip unZip) throws IOException {
        File destDir = new File(unZip.getTargetPath());
        if (!destDir.exists()) {
            boolean created = destDir.mkdirs(); // 创建目标目录
            if (created) {
                log.info("目录创建成功: {}", unZip.getTargetPath());
            } else {
                log.warn("目录已存在或创建失败: {}", unZip.getTargetPath());
            }
        }

        List<String> charsets = Arrays.asList("GBK", "UTF-8", "ISO-8859-1");

        // 尝试使用每种字符集
        for (String charset : charsets) {
            try (ZipFile zip = new ZipFile(unZip.getZipPath(), charset)) {
                Enumeration<? extends ZipArchiveEntry> entries = zip.getEntries();
                boolean allFilesValid = true; // 标记是否所有文件名都有效
                while (entries.hasMoreElements()) {
                    ZipArchiveEntry entry = entries.nextElement();
                    String name = entry.getName();
                    name = name.replaceAll("[/\\\\]+$", "");
                    // 校验文件名 是否解析乱码
                    if (!fileCustomUtils.isValidFileName(name)) {
                        // 此字符解码失败，尝试其他字符集
                        allFilesValid = false; // 标记为无效
                        break;  // 跳出循环，尝试下一个字符集
                    }
                    // 处理文件
                    try {
                        File outFile = new File(destDir, entry.getName());
                        // 如果是目录，则创建目录
                        if (entry.isDirectory()) {
                            if (!outFile.mkdirs()) {
                                log.warn("目录已存在或创建失败: {}", outFile.getPath());
                            }
                        } else {
                            // 如果是文件，则创建父目录并写入文件
                            Files.createDirectories(outFile.getParentFile().toPath());

                            try (InputStream inputStream = zip.getInputStream(entry);
                                 BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(outFile.toPath()))) {
                                byte[] data = fileCustomUtils.readInputStream(inputStream); // 使用自定义的读取方法
                                bos.write(data); // 写入文件
                            } catch (IOException e) {
                                log.error("写入文件失败: {}, 错误信息: {}", outFile.getPath(), e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取文件路径失败: {}, 错误信息: {}", entry.getName(), e.getMessage());
                    }
                }
                // 如果所有文件都有效，则返回
                if (allFilesValid) {
                    log.info("成功使用字符集 {} 解压所有文件", charset);
                    return true; // 退出方法
                }
            } catch (IOException e) {
                log.error("使用字符集 {} 解压失败,请重新设置文件名: {}", charset, e.getMessage());
                // 可以选择继续尝试其他字符集
            }
        }
        // 如果所有字符集都失败，抛出异常
        throw new IOException("所有字符集解压失败");
    }

    /**
     * @param filepathList 文件地址集合
     * @description: 根据文件地址批量删除文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 16:39
     * @param: filepathList
     * @return: Boolean
     */
    @Override
    public void deleteFiles(List<String> filepathList) {
        fileCustomUtils.deleteFiles(filepathList);
    }

    /**
     * @description: 根据文件地址批量 延迟删除 文件
     * @author: zhaoTianQi
     * @date: 2024/11/20 11:21
     * @param: FileDeletionParamsDto
     * @return: void
     **/
    @Override
    public void scheduleFileDeletion(FileDeletionParamsDto fileDeletionParamsDto) {
        fileCustomUtils.scheduleFileDeletion(fileDeletionParamsDto.getFilepathList(), fileDeletionParamsDto.getDelayMinutes());
    }


    //	--------------------------------------------------------------------------------------
    public int getRandom() {
        Random random = new Random();
        int min = 1; // 最小值，确保生成的值大于零
        int max = Integer.MAX_VALUE; // 最大值
        int randomInt = random.nextInt(max - min + 1) + min;
        return randomInt;
    }

	public static void main(String[] args) {
        try {
            System.out.println(getPPTSize(new File("C:\\Users\\<USER>\\Desktop\\证书\\新建 PPT 演示文稿.ppt")));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
	public static int getPPTSize(File pptFile) throws Exception {
		// 设置允许的最小解压比，将其设得比默认更低，如 0.001
		// 如果文件过大，可能会导致内存溢出，需要根据实际情况进行设置。
		// 默认值为 0.05，表示允许的最小解压比为 5%。
		// 设置为 0.001 可允许更小的解压比，但可能会导致内存溢出。
		// 0.001 表示允许的最小解压比为 0.1%，即 1/1000。
		//建议捕获错误，防止文件过大导致内存溢出。，使用默认值，前端并提示用户进行更改
		ZipSecureFile.setMinInflateRatio(0.001); // 允许更高的压缩比
		String flag = check_ppt(pptFile);
		//判断文件类型,如果时ppt文件则走HSLFSlideShow  如果时pptx则走XMLSlideShow,
        int pptSize = 0;
		if (flag.equals("ppt")){
			//ppt文件
			pptSize = getPptSizeFromPpt(pptFile);
		}else if(flag.equals("pptx")){
			//pptx文件
			pptSize = getPptSizeFromPptx(pptFile);
		}else {
			throw new Exception("文件错误");
        }

        return pptSize;
    }
    public List<String> splidePpt(String pptFile, String outputDir, String prefix) throws Exception {

//        generateSingleImagesFromPPT2(pptFile, outputDir, "false");
//        // 处理ppt文字字体
//        dealPptFontsToImg(pptFile, "宋体");
		// 检查ppt中的每页是否含有视频并记录数据库
		Long presentationId = Long.valueOf(outputDir.substring(outputDir.lastIndexOf("/") + 1));

		List<String> list = new ArrayList<>();
		//加载测试文档1
		Presentation ppt1 = new Presentation();
		System.out.println("============================" + pptFile);
		ppt1.loadFromFile(pptFile);
		//遍历文档1
		for (int i = 0; i < ppt1.getSlides().getCount(); i++) {

            //新建一个PPT文档，并移除默认生成的第一页幻灯片
            Presentation newppt = new Presentation();
            newppt.getSlides().removeAt(0);

            //将每一页添加到新建的文档，并保存
            newppt.getSlides().append(ppt1.getSlides().get(i));
            String format = outputDir + "/" + prefix + String.format("_%1$s.pptx", (i + 1));
            list.add(format);
            newppt.saveToFile(format, FileFormat.PPTX_2013);
            newppt.dispose();
            // 处理单页ppt生成图片
            dealPpToImg(format);
        }
        ppt1.dispose();
        return list;

	}

	private static void dealPpToImg(String path) throws Exception {
		//load an example PPTX file
		Presentation presentation = new Presentation();
		presentation.loadFromFile(path);
		//loop through the slides
		for (int j = 0; j < presentation.getSlides().getCount(); j++) {
			//save each slide as a BufferedImage
			BufferedImage image = presentation.getSlides().get(j).saveAsImage();
			// Step 2: 创建新的 BufferedImage，并指定拉伸后的大小
			double scale = 2.5; // 适当放大但不变形
			int width = (int)(image.getWidth() * scale);
			int height = (int)(image.getHeight() * scale);
			BufferedImage resizedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
			Graphics2D graphics = resizedImage.createGraphics();
			// 拉伸绘制
			graphics.drawImage(image, 0, 0, width, height, null);
			graphics.dispose();

			// 使用更精确的图像缩放算法
			graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
			graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);

			// 应用字体平滑和锐化
			graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);

			// Step 3: 保存拉伸后的图片
			String fileName = path.replace(".pptx", ".jpg");
			ImageIO.write(resizedImage, "JPG", new File(fileName));
            //save BufferedImage as PNG file format
//            String fileName = path.replace(".pptx", ".jpg");
//            ImageIO.write(image, "PNG", new File(fileName));
        }
        ThumbnailGenerator(path);
        presentation.dispose();
    }

    private static void ThumbnailGenerator(String format) throws Exception {
        String originalFilePath = format.replace(".pptx", ".jpg");
        String thumbnailFilePath = format.replace(".pptx", "WH.jpg");

        try {
            Thumbnails.of(originalFilePath)
                    .size(1247, 720) // 最大宽度和高度
                    .keepAspectRatio(true) // 保持宽高比
                    .toFile(thumbnailFilePath); // 保存缩略图
            System.out.println("缩略图已生成: " + thumbnailFilePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String[] getFile(String path) {
        // 文件路径
        String filePath = path;
        String[] split = null;
        try {
            // 读取文件内容，并去掉空格和空白行
            String content = Files.lines(Paths.get(filePath))
                    .map(String::trim) // 去掉每行的前后空白
                    .filter(line -> !line.isEmpty()) // 过滤掉空白行
                    .collect(Collectors.joining()); // 合并为一个字符串

            // 输出处理后的内容

            // 输出内容
            split = content.split("@page");

//            int i=1;
//            for (String s:split) {
//                System.out.println("第" + i + "页内容：");
//                System.out.println(s);
//                i++;
//            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return split;
    }

    @Override
    public List<String> processPPT(String pptFile, String outputDir, String slidePrefix) throws Exception {
        return splidePpt(pptFile, outputDir, slidePrefix);
    }

    @Override
    public int moveFile(String oldPath, String newPath) {

        //文件从presentationTempPath  -》 presentationPath
        java.nio.file.Path tempPath = Paths.get(oldPath);
        java.nio.file.Path destinationPath = Paths.get(newPath);

        if (!new File(oldPath).exists()) {
            return 0;
        }

        try {
            // 创建目标目录（如果不存在）
            Files.createDirectories(destinationPath.getParent());
            // 移动文件
            Files.move(tempPath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("文件已成功移动到: " + newPath);
            // 确保源文件已删除（通常文件移动后源文件已被删除，但此步骤是双重检查）
            if (Files.exists(tempPath)) {
                Files.delete(tempPath);
                System.out.println("源文件已成功删除: " + oldPath);
            }
            return 1;
        } catch (IOException e) {
            System.err.println("移动文件时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("移动文件时发生错误");
        }


    }

    // 文件删除方法
    @Override
    public void deleteFile(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {
            // 记录异常日志，或者根据需求处理
            System.err.println("删除文件失败: " + e.getMessage());
            // 根据需要抛出自定义异常
            throw new CustomException("删除文件失败: " + file.getAbsolutePath(), e);
        }
    }

    // 目录删除方法
    @Override
    public void deleteDirectory(File directory) {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File f : files) {
                    deleteFile(f);
                }
            }
            try {
                if (!directory.delete()) {
                    throw new IOException("删除文件夹失败: " + directory.getAbsolutePath());
                }
            } catch (Exception e) {
                // 记录异常日志，或者根据需求处理
                System.err.println("删除文件夹失败: " + e.getMessage());
                // 根据需要抛出自定义异常
                throw new CustomException("删除文件夹失败: " + directory.getAbsolutePath(), e);
            }
        }
    }

    @Override
	public Map<String, Object> uploadPPtTemp(MultipartFile file, String modeltype) throws Exception {

        FileVo fileVo = this.uploadFile(file, temp);

        if (fileVo == null) {
            throw new CustomException("上传ppt文件过大");
        }
        String fileName = fileVo.getName().replace(".pptx", "");
        String path = fileVo.getPreview();        //D:/ruoyi/uploadDataPath/temp/pptx/0/20240816/1723800157510848.pptx
        int presentationId = getRandom();
        int pptSize = 0;
        String sourcePath = path.replace("\\", "/");
        String sourcepathOut = path.replace("\\", "/").replace(temp, modeltype);

        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            try {
                pptSize = this.getPPTSize(new File(path));
            } catch (Exception e) {
				throw new Exception("文件错误");
            }
            sourcePath = path.replace(localFilePathWin, "").replace("\\", "/").replace(temp, modeltype);
		} else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            try {
                pptSize = this.getPPTSize(new File(path));
            } catch (Exception e) {
				throw new Exception("文件错误");
            }
            sourcePath = path.replace(localFilePathLinux, "").replace("\\", "/").replace(temp, modeltype);
		} else {
			throw new UnsupportedOperationException("Unsupported operating system: " + os);
		}
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("presentationId", presentationId);
		resultMap.put("presentationPath", sourcepathOut);
		resultMap.put("presentationHttp", sourcePath);
		resultMap.put("pptAllPage", pptSize);
		resultMap.put("fileName", fileName);
		resultMap.put("fileid", fileVo.getId());
		return resultMap;
	}

    @Override
    public Map<String, Object> uploadSpeechDraft(MultipartFile file, String modeltype) {
        FileVo fileVo = this.uploadFile(file, temp);

        if (fileVo == null) {
            throw new CustomException("上传文件过大");
        } else {
            String tempPath = fileVo.getPreview().replace("\\", "/");

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("SpeechDraftName", fileVo.getName());
            resultMap.put("SpeechDraftPath", tempPath.replace(temp, modeltype));
            //resultMap.put("SpeechDraftPath",tempPath.replace("temp",modeltype));
            resultMap.put("SpeechDraftId", fileVo.getId());

            return resultMap;
        }
    }
    //	--------------------------------------------------------------------------------------

    /**
     * @param path 路径
     * @description: 上传到指定位置文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 17:17
     * @param: file
     * @return: Map<String, String>
     **/
    @Override
    public Map<String, String> uploadToTargetLocation(MultipartFile file, String path) throws IOException {
        // 创建目标文件
        File destinationFile = new File(path);
        if (destinationFile.isDirectory()) {
            destinationFile = new File(path + File.separator + file.getOriginalFilename());
        }

        // 检查目标目录是否存在，若不存在则创建
        fileCustomUtils.checkAndCreateFile(destinationFile.getAbsolutePath());

        // 创建文件并写入内容
        file.transferTo(destinationFile);

        // 返回文件信息
        HashMap<String, String> map = new HashMap<>();
        map.put("path", fileCustomUtils.normalizePath(destinationFile.getAbsolutePath()));
        map.put("name", file.getName());
        map.put("originalFilename", file.getOriginalFilename());
        map.put("size", String.valueOf(file.getSize()));
        map.put("contentType", file.getContentType());
        return map;
    }


    /**
     * @param request  请求
     * @param response 响应
     * @description: 根据文件地址下载文件
     * @author: zhaoTianQi
     * @date: 2024/11/20 10:22
     * @param: downLoadQuery
     * @return: void
     **/
    @Override
    public void customDownLoad(DownLoadQuery downLoadQuery, HttpServletRequest request, HttpServletResponse response) {
        File file = new File(downLoadQuery.getFilePath());
        String fileName = file.getName();
        if (StringUtils.isNotBlank(downLoadQuery.getFileName())) {
            fileName = downLoadQuery.getFileName() + "." + fileCustomUtils.getFileSuffix(file);
        }
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(fileName, "utf8") + "\"");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Length", String.valueOf(file.length()));
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("编码格式不支持", e);
        }

        try (InputStream inputStream = new BufferedInputStream(Files.newInputStream(file.toPath()));
             OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
            log.info("文件 {} 写入成功，大小为 {} 字节", file.getName(), file.length());
        } catch (IOException e) {
            throw new RuntimeException("文件下载失败", e);
        }
    }


	/**
	 * @description: 检查并创建目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 10:23
	 * @param: path 路径
	 * @return: AjaxResult
	 **/
	@Override
	public String checkAndCreateDirectory(String path) {
		fileCustomUtils.checkAndCreateDirectory(path);
		return "目录创建成功" + path;
	}


    /**
     * @description: 根据内容创建文件
     * @author: zhaoTianQi
     * @date: 2024/11/21 14:40
     * @param: textFileCreationDto
     * @return: String
     */
    @Override
    public String textFileCreate(TextFileCreationDto textFileCreationDto) {
        // 创建文件对象
        File file = new File(textFileCreationDto.getFilePath());

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            // 将内容写入文件
            writer.write(textFileCreationDto.getText());
            log.info("文件 {} 写入成功", file.getName());
        } catch (IOException e) {
            // 处理异常
            throw new RuntimeException(e);
        }
        return file.getAbsolutePath();
    }

    /**
     * @description: 获取文件字节信息
     * @author: zhaoTianQi
     * @date: 2024/11/21 14:55
     * @param: filePath
     * @return: ResponseEntity<byte>
     */
    @Override
    public ResponseEntity<byte[]> getFileBytes(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            log.error("文件路径filePath为空");
            return null;
        }
        File file = new File(filePath);

        if (!file.exists() || file.isDirectory()) {
            throw new RuntimeException("文件不存在或路径是一个目录: " + filePath);
        }

		try {
			byte[] fileBytes = Files.readAllBytes(file.toPath()); // 读取文件到字节数组
			// URL 编码文件名以支持中文
			String encodedFileName = URLEncoder.encode(file.getName(), StandardCharsets.UTF_8.name());

			log.info("文件 {} 读取成功，大小为 {} 字节", file.getName(), fileBytes.length);
			return ResponseEntity.ok()
					.header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName)
					.contentType(MediaType.APPLICATION_OCTET_STREAM)
					.contentLength(file.length())
					.body(fileBytes);
		} catch (IOException e) {
			throw new RuntimeException("读取文件失败: " + filePath, e);
		}
	}


	/**
	 * 清理不需要的文件信息
	 * 本方法根据不同的业务模型和文件创建时间来决定是否删除文件信息
	 * 主要逻辑包括：
	 * 1. 获取所有文件信息
	 * 2. 根据业务模型和创建时间判断文件是否需要被删除
	 * 3. 删除符合条件的文件信息
	 */
	@Override
	public void fileClean() {
	    //拿到sys_file_info的所有数据放入SysFileInfo的列表
	    List<SysFileInfo> sysFileInfoList = sysFileInfoMapper.selectListAll();
	    //创建需要删除的主键列表
	    List<Long> nullBusiIdList = new ArrayList<>();
	    //遍历列表
	    for (SysFileInfo sysFileInfo : sysFileInfoList) {
	        //首先排除不能被删除的数据，如果满足特定的条件就跳过这个数据不加入删除列表
	        if(Objects.equals(sysFileInfo.getBusiModel(), "img") || Objects.equals(sysFileInfo.getBusiModel(), "yd1")){
	            continue;
	        }
	        //其次是临时使用的文件使用过就要删除自建知识库-去解析功能与智能生成ppt
	        if(Objects.equals(sysFileInfo.getBusiModel(), "jx1") || Objects.equals(sysFileInfo.getBusiModel(), "smartPPT")){
	            LocalDateTime createTime = sysFileInfo.getCreateTime()
	                    .toInstant()
	                    .atZone(ZoneId.systemDefault())
	                    .toLocalDateTime();

	            // 判断是否超过一周
	            if (ChronoUnit.DAYS.between(createTime, LocalDateTime.now()) >= 7) {
	                //如果是，则将主键添加到列表中
	                nullBusiIdList.add(sysFileInfo.getId());
	            }
	        }
	        //busi_id为空且超过一周
	        //检查busi_id是否为空
	        if (StringUtils.isBlank(sysFileInfo.getBusiId())) {
	            // 把 Date 类型转换成 LocalDateTime
	            LocalDateTime createTime = sysFileInfo.getCreateTime()
	                    .toInstant()
	                    .atZone(ZoneId.systemDefault())
	                    .toLocalDateTime();

	            // 判断是否超过一周
	            if (ChronoUnit.DAYS.between(createTime, LocalDateTime.now()) >= 7) {
	                //如果是，则将主键添加到列表中
	                nullBusiIdList.add(sysFileInfo.getId());
	            }
	        }
	    }
	    //根据主键去删除表和文件
	    deleteByIdList(nullBusiIdList);
	}

	/**
	 * 根据业务ID列表删除文件和数据库记录
	 * 此方法旨在清理数据库中和文件系统中不再需要的文件记录和实际文件
	 * 它首先从数据库中获取每个业务ID对应的文件路径，然后删除该文件，
	 * 最后从数据库中删除对应的记录
	 *
	 * @param nullBusiIdList 一个包含多个业务ID的列表，类型为List<Long>
	 *                       这些ID对应于需要被删除的数据库记录和文件
	 */
	private void deleteByIdList(List<Long> nullBusiIdList) {
	    //根据主键去删除表和文件
	    for (Long id : nullBusiIdList) {
	        //根据主键获得文件路径
	        String filePath = sysFileInfoMapper.selectSysFileInfoById(id).getFilePath();
	        //根据文件路径删除文件
	        fileCustomUtils.deleteFiles(Arrays.asList(filePath));
	        //根据主键删除表数据
	        sysFileInfoMapper.deleteSysFileInfoById(id);
	    }
	}
}
