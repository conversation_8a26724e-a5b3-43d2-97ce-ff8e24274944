package com.ruoyi.file.event;

import org.springframework.context.ApplicationEvent;

public class Tevent extends ApplicationEvent {
    public Tevent(Object source) {
        super(source);
    }

    private String sourcePath;
    private String outputPath;
    private String slidePrefix;

    public Tevent(Object source, String sourcePath,String outputPath,String slidePrefix) {
        super(source);
        this.sourcePath=sourcePath;
        this.outputPath=outputPath;
        this.slidePrefix=slidePrefix;
    }

    public String getSlidePrefix() {
        return slidePrefix;
    }

    public void setSlidePrefix(String slidePrefix) {
        this.slidePrefix = slidePrefix;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getOutputPath() {
        return outputPath;
    }

    public void setOutputPath(String outputPath) {
        this.outputPath = outputPath;
    }
}

