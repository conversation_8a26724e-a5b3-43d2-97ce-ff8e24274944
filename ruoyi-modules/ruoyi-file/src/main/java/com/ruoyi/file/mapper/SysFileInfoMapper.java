package com.ruoyi.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.file.domain.SysFileInfo;

import java.util.List;

/**
 * 文件信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-13
 */

public interface SysFileInfoMapper extends BaseMapper<SysFileInfo>
{
    /**
     * 查询文件信息
     *
     * @param id 文件信息主键
     * @return 文件信息
     */
    public SysFileInfo selectSysFileInfoById(Long id);

    /**
     * 查询文件信息列表
     *
     * @param sysFileInfo 文件信息
     * @return 文件信息集合
     */
    public List<SysFileInfo> selectSysFileInfoList(SysFileInfo sysFileInfo);

    /**
     * 删除文件信息
     *
     * @param id 文件信息主键
     * @return 结果
     */
    public int deleteSysFileInfoById(Long id);

    /**
     * 批量删除文件信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysFileInfoByIds(Long[] ids);

    /**
     * 通过业务id获取门禁照片地址
     */
    String selectSysFileInfoByBusiId(String busiId);

    int insert(SysFileInfo sysFileInfo);


    List<SysFileInfo>  getFileInfoListByWbryIds(String busiId);

    List<SysFileInfo> listByIds(List<Long> asList);

    int saveOrUpdateBatch(SysFileInfo sysFileInfoList);

    List<SysFileInfo>  selectSysFileInfoByFileObjectName(String busiId);

    List<SysFileInfo> getSysFileInfoByBusiId(String busiId);


    SysFileInfo selOneSysFileInfoByFileObjectName(String fileObjectName);

    List<SysFileInfo> selectListAll();
}
