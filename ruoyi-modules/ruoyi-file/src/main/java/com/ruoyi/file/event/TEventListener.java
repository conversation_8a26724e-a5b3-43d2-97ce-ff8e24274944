package com.ruoyi.file.event;


import com.ruoyi.file.exception.CustomException;
import com.ruoyi.file.service.ISysFileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class TEventListener {
    //这里使用了@Async注解异步线程执行,起作用的前提是开启@EnableAsync注解
    @Resource
    private ISysFileInfoService sysFileInfoService;

    @EventListener
    @Async
    public void handleOrderEvent(Tevent tevent) throws Exception {
        log.info("执行ppt分割处理");
        //List<String> list = presentationPathService.processPPT(new File(tevent.getSourcePath()), tevent.getOutputPath(), tevent.getSlidePrefix());
        List<String> list = sysFileInfoService.processPPT(tevent.getSourcePath(), tevent.getOutputPath(), tevent.getSlidePrefix());
        if(list == null){
            log.info("ppt文件分割异常");
            throw new CustomException("ppt文件分割异常");
        }
    }
}
