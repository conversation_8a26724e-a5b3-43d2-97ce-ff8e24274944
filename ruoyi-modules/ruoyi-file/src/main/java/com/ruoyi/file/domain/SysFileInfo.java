package com.ruoyi.file.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

/**
 * 文件信息对象 sys_file_info
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
@Data
@TableName("sys_file_info")
public class SysFileInfo
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /** 所属业务模块 */
    @Excel(name = "所属业务模块")
    @TableField("busi_model")
    private String busiModel;

    /** 业务标识id */
    @Excel(name = "业务标识id")
    @TableField("busi_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String busiId;

    /** 文件存储位置(1磁盘2oss) */
    @Excel(name = "文件存储位置(1磁盘2oss)")
    @TableField("file_location")
    private Integer fileLocation;

    /** 文件名称（上传时候的文件名） */
    @Excel(name = "文件名称", readConverterExp = "上=传时候的文件名")
    @TableField("file_origin_name")
    private String fileOriginName;

    /** 文件后缀 */
    @Excel(name = "文件后缀")
    @TableField("file_suffix")
    private String fileSuffix;

    /** 文件大小kb */
    @Excel(name = "文件大小kb")
    @TableField("file_size_kb")
    private Long fileSizeKb;

    /** 文件唯一标识id */
    @Excel(name = "文件唯一标识id")
    @TableField("file_object_name")
    private String fileObjectName;

    /** 存储路径 */
    @Excel(name = "存储路径")
    @TableField("file_path")
    private String filePath;

    @TableField("create_time")
    private Date createTime;

    @TableField("create_by")
    private Long createBy;

    @TableField("update_by")
    private Long updateBy;

    @TableField("update_time")
    private Date updateTime;

    @TableField("remark")
    private String remark;

    @TableField("create_user_name")
    private String createUserName;

    @TableField(exist = false)
    private String createUser;
}
