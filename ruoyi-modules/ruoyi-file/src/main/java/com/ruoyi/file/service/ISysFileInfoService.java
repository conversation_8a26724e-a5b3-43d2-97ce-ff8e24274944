package com.ruoyi.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.file.domain.*;
import com.ruoyi.system.api.dto.FileDeletionParamsDto;
import com.ruoyi.system.api.dto.TextFileCreationDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 文件信息Service接口
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
public interface ISysFileInfoService extends IService<SysFileInfo> {
    /**
     * 查询文件信息
     *
     * @param id 文件信息主键
     * @return 文件信息
     */
    public SysFileInfo selectSysFileInfoById(Long id);

    /**
     * 查询文件信息列表
     *
     * @param sysFileInfo 文件信息
     * @return 文件信息集合
     */
    public List<SysFileInfo> selectSysFileInfoList(SysFileInfo sysFileInfo);

    /**
     * 新增文件信息
     *
     * @param sysFileInfo 文件信息
     * @return 结果
     */
    public boolean insertSysFileInfo(SysFileInfo sysFileInfo);

    /**
     * 修改文件信息
     *
     * @param sysFileInfo 文件信息
     * @return 结果
     */
    public boolean updateSysFileInfo(SysFileInfo sysFileInfo);

    /**
     * 批量删除文件信息
     *
     * @param ids 需要删除的文件信息主键集合
     * @return 结果
     */
    public int deleteSysFileInfoByIds(Long[] ids);

    /**
     * 删除文件信息信息
     *
     * @param id 文件信息主键
     * @return 结果
     */
    public int deleteSysFileInfoById(Long id);

    /**
     * 上传文件，返回文件的唯一标识
     *
     * @param file 要上传的文件
     * @return 文件id
     * <AUTHOR>
     * @date 2020/8/17 21:21
     */
    FileVo uploadFile(MultipartFile file, String modeltype);
    FileVo uploadFile(MultipartFile file, String modeltype, String fileName);

    
    /**
     * 文件预览，目前只支持图片预览
     *
     * @param id       文件id
     * @param response 相应流
     */
    void preview(Long id, HttpServletResponse response) throws IOException;

    void previewPath(String path, HttpServletResponse response) throws IOException;


    List<SysFileInfo> getFileInfoListByWbryIds(Long[] wbryIds);


    void fileDownload(Long id, HttpServletRequest request, HttpServletResponse response);

    FileVo uploadForTask(MultipartFile file, String modeltype, Long taskId);

    void fileDownloadByFileObjectName(String fileObjectName, HttpServletRequest request, HttpServletResponse response);

    int relationFile(Long[] ids, String busiId);

    List<SysFileInfo> getFileInfo(String busiId);

    void deleteFile(String busiId);

    SysFileInfo getFileInfoByFileId(String id);


    /**
     * @description: 解压文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 15:59
     * @param: unZip
     * @return: Integer
     **/
    Boolean extractZipFile(UnZip unZip) throws IOException;

    /**
     * @description: 根据文件地址批量删除文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 16:39
     * @param: filepathList
     **/
    void deleteFiles(List<String> filepathList);


    /**
     * @description: 根据文件地址批量 延迟删除 文件
     * @author: zhaoTianQi
     * @date: 2024/11/20 11:20
     * @param: FileDeletionParamsDto
     * @return: void
     **/
    void scheduleFileDeletion(FileDeletionParamsDto fileDeletionParamsDto);

    /**
     * @description: 上传到指定位置文件
     * @author: zhaoTianQi
     * @date: 2024/11/19 17:17
     * @param: file
     * @param path 路径
     * @return: Map<String, String>
     **/
    Map<String, String> uploadToTargetLocation(MultipartFile file, String path) throws IOException;

    /**
     * @description: 根据文件地址下载文件
     * @author: zhaoTianQi
     * @date: 2024/11/20 10:22
     * @param: downLoadQuery
     * @param request 请求
     * @param response 响应
     * @return: void
     **/
    void customDownLoad(DownLoadQuery downLoadQuery, HttpServletRequest request, HttpServletResponse response);

//    -----------------------------------------------------------------------------------
    int moveFile(String oldPath, String newPath);

    List<String> processPPT(String pptFile, String outputDir, String slidePrefix) throws Exception;

    Map<String, Object> uploadSpeechDraft(MultipartFile file,String modeltype);

    Map<String, Object> uploadPPtTemp(MultipartFile file,String modeltype) throws Exception;

    void deleteFile(File file);

    void deleteDirectory(File directory);

    String[] getFile(String path);
//    -----------------------------------------------------------------------------------


    /**
     * @description: 检查并创建目录
     * @author: zhaoTianQi
     * @date: 2024/11/20 10:23
     * @param: path 路径
     * @return: String
     **/
    String checkAndCreateDirectory(String path);

    /**
     * @description: 根据内容创建文件
     * @author: zhaoTianQi 
     * @date: 2024/11/21 14:40
     * @param: textFileCreationDto
     * @return: String
     **/
	String textFileCreate(TextFileCreationDto textFileCreationDto);

    /**
     * @description: 获取文件字节信息
     * @author: zhaoTianQi 
     * @date: 2024/11/21 14:55
     * @param: filePath
     * @return: ResponseEntity<byte>
     **/
    ResponseEntity<byte[]> getFileBytes(String filePath);


    void fileClean();
}
