package com.ruoyi.file.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class FileCustomUtils {

	/**
	 * @description: 批量删除文件或目录，并在目录为空时删除空目录。
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:25
	 * @param: paths
	 * @return: void
	 **/
	public void deleteFiles(List<String> paths) {
		for (String path : paths) {
			File file = new File(path);

			// 如果文件或目录不存在，跳过
			if (!file.exists()) {
				System.out.println("文件或目录不存在: " + path);
				continue;
			}

			// 递归删除文件或目录
			deleteRecursively(file);

			// 删除父目录（如果为空）
			File parentFile = file.getParentFile();
			deleteEmptyParentDirectories(parentFile);
		}
	}

	/**
	 * @description: 递归删除文件或目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:25
	 * @param: file
	 * @return: void
	 **/
	private void deleteRecursively(File file) {
		if (file.isDirectory()) {
			File[] files = file.listFiles();
			if (files != null) {
				for (File child : files) {
					deleteRecursively(child);  // 递归删除子文件或子目录
				}
			}
		}

		// 删除文件或空目录
		if (file.delete()) {
			log.info("删除成功：{}", file.getAbsolutePath());
		} else {
			log.error("删除失败：{}", file.getAbsolutePath());
		}
	}

	/**
	 * @description: 递归删除空的父目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:26
	 * @param: directory
	 * @return: void
	 **/
	private void deleteEmptyParentDirectories(File directory) {
		// 如果父目录为空并且存在，递归删除
		while (directory != null && directory.isDirectory() && Objects.requireNonNull(directory.list()).length == 0) {
			if (directory.delete()) {
				log.info("空目录已删除: {}", directory.getAbsolutePath());
			} else {
				log.error("无法删除空目录: {}", directory.getAbsolutePath());
			}

			// 获取上一级目录
			directory = directory.getParentFile();
		}
	}


	/**
	 * @description: 检查并创建目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:26
	 * @param: outFileFolder
	 * @return: void
	 **/
	public void checkAndCreateDirectory(String outFileFolder) {
		Path path = Paths.get(outFileFolder);
		if (Files.exists(path)) {
			log.info("目录已存在：{}", path);
		} else {
			try {
				Files.createDirectories(path);
				log.info("目录创建成功：{}", path);
			} catch (IOException e) {
				log.error("目录创建失败：{}", e.getMessage());
				throw new RuntimeException(e);
			}
		}
	}

	/**
	 * @description: 检查并创建目录及文件
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:26
	 * @param: filePath
	 * @return: void
	 **/
	public void checkAndCreateFile(String filePath) {
		Path path = Paths.get(filePath);
		Path directory = path.getParent(); // 获取文件的目录部分

		// 检查并创建目录
		if (directory != null) {
			checkAndCreateDirectory(directory.toString());
		}

		// 创建文件
		try {
			if (!Files.exists(path)) {
				Files.createFile(path); // 创建文件
				log.info("文件创建成功：{}", path);
			} else {
				log.info("文件已存在：{}", path);
			}
		} catch (IOException e) {
			log.error("文件创建失败：{}", e.getMessage());
			throw new RuntimeException(e);
		}
	}


	/**
	 * @description:  将路径中的所有分隔符统一为正斜杠 "/"，并去除重复的斜杠
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:26
	 * @param: path 原始路径
	 * @return: String 格式化后的路径
	 **/
	public String normalizePath(String path) {
		// 将所有反斜杠和连续斜杠替换为单个正斜杠
		return path.replaceAll("[\\\\/]+", "/");
	}


	/**
	 * @description:  检查文件名是否合法
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: fileName
	 * @return: boolean
	 **/
	public boolean isValidFileName(String fileName) {
		// 正则表达式允许字母、数字、汉字和一些常见符号
		String regex = "^[a-zA-Z0-9\\u4E00-\\u9FA5-_\\s\\.]+(/([a-zA-Z0-9\\u4E00-\\u9FA5-_\\s\\.]+))*$"; // 允许的字符范围
		return fileName.matches(regex);
	}

	/**
	 * @description: 安排文件延迟删除 可以是目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: filePath
	 * @param delayMinutes 延迟分钟数
	 * @return: void
	 **/
	public void scheduleFileDeletion(String filePath, int delayMinutes) {
		ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
		scheduler.schedule(() -> {
			File file = new File(filePath);
			deleteRecursively(file);
			log.info("延迟：{} 分钟后 文件删除执行成功: {}", delayMinutes, file.getAbsolutePath());
		}, delayMinutes, TimeUnit.MINUTES);
	}

	/**
	 * @description: 安排文件延迟删除 可以是目录
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: filepathList
	 * @param delayMinutes 延迟分钟数
	 * @return: void
	 **/
	public void scheduleFileDeletion(List<String> filepathList, Integer delayMinutes) {
		if (filepathList == null || filepathList.isEmpty()) {
			return;
		}
		if (delayMinutes == null) {
			delayMinutes = 5;
		}
		// 使用 ScheduledExecutorService 来延迟执行文件删除任务
		ScheduledExecutorService scheduler  = Executors.newScheduledThreadPool(1);
		for (String path : filepathList) {
			File file = new File(path);
			if (!file.exists()) {
				System.out.println("文件或目录不存在: " + path);
				continue;
			}
			scheduler.schedule(() -> {
				// 递归删除文件或目录
				deleteRecursively(file);
				// 删除父目录（如果为空）
				File parentFile = file.getParentFile();
				deleteEmptyParentDirectories(parentFile);
			}, delayMinutes, TimeUnit.MINUTES);
		}
	}

	/**
	 * @description:  自定义方法：读取 InputStream 中的所有字节
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: inputStream
	 * @return: byte
	 **/
	public byte[] readInputStream(InputStream inputStream) throws IOException {
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		byte[] buffer = new byte[4096];
		int bytesRead;

		while ((bytesRead = inputStream.read(buffer)) != -1) {
			byteArrayOutputStream.write(buffer, 0, bytesRead);
		}

		return byteArrayOutputStream.toByteArray();
	}

	/**
	 * @description: 获取文件后缀
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: fileName
	 * @return: String
	 **/
	public String getFileSuffix(String fileName) {
		if (StringUtils.isBlank(fileName)) {
			return "";
		}
		int dotIndex = fileName.lastIndexOf(".");
		if (dotIndex == -1) {
			return "";
		}
		return fileName.substring(dotIndex + 1);
	}

	/**
	 * @description: 获取文件后缀
	 * @author: zhaoTianQi
	 * @date: 2024/11/20 9:27
	 * @param: file
	 * @return: String
	 **/
	public String getFileSuffix(File file) {
		if (file == null) {
			return "";
		}
		int dotIndex = file.getName().lastIndexOf(".");
		if (dotIndex == -1) {
			return "";
		}
		return file.getName().substring(dotIndex + 1);
	}

}
