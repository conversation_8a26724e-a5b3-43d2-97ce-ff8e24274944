package com.ruoyi.common.core.enums;

import java.util.Arrays;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum QuestionTypes
{
    SINGLE("single", "单项选择题"),
    MULTIPLE("multiple", "多项选择题"),
    BLANK("blank", "填空题"),
    SHORTANSWER("shortAnswer", "简答题"),
    NOUNDEFINITION("nounDefinition", "名词解释题"),
    CASEANALYSIS("caseAnalysis", "案例分析题"),
    ESSAYQUESTION("essayQuestion", "论述题");

    private final String code;
    private final String info;

    QuestionTypes(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    public static String getInfoByCode(String code) {
        String info = null;

        switch (code) {
            case "single" :
                info = "单项选择题";
                break;
            case "multiple" :
                info = "多项选择题";
                break;
            case "blank" :
                info = "填空题";
                break;
            case "shortAnswer" :
                info = "简答题";
                break;
            case "nounDefinition" :
                info = "名词解释题";
                break;
            case "caseAnalysis" :
                info = "案例分析题";
                break;
            case "essayQuestion" :
                info = "论述题";
                break;
            default:
                break;
        }

        return info;
    }

    public static QuestionTypes getEnumByType(String type){
        if (type == null){
            return null;
        }
        return Arrays.stream(QuestionTypes.values())
                .filter(item -> item.code.equals(type))
                .findFirst().orElse(null);
    }
}
