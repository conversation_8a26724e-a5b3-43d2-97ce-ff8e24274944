package com.ruoyi.file.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SecureFileUpload {
    String[] allowExtensions() default {}; // 允许的扩展名

    int maxSizeMb() default 50;

    /**
     * 是否将 allowExtensions 与默认扩展名合并（默认 false，即覆盖）
     */
    boolean mergeWithDefault() default false;



    // 示例
    // // 覆盖模式（默认）
    // @SecureFileUpload(allowExtensions = {"txt", "md"})
    //  *
    // // 合并默认+自定义
    // @SecureFileUpload(allowExtensions = {"md"}, mergeWithDefault = true)
    //

}
