package com.ruoyi.file.util;

import com.ruoyi.file.constant.FileTypeRegistry;

import java.io.InputStream;
import java.util.Map;

/**
 * 文件魔数类型识别工具
 */
public class FileMagicDetector {

    /**
     * 检测文件输入流的类型
     * @param is 输入流
     * @return 文件类型（如 jpg、pdf），识别失败返回 null
     */
    public static String detectType(InputStream is) {
        try {
            byte[] header = new byte[16];
            int read = is.read(header);
            if (read == -1) return null;

            String hex = bytesToHex(header);
            for (Map.Entry<String, String> entry : FileTypeRegistry.MAGIC_TYPE_MAP.entrySet()) {
                if (hex.startsWith(entry.getKey())) {
                    return entry.getValue();
                }
            }
        } catch (Exception ignored) {}
        return null;
    }

    /**
     * 字节数组转16进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) sb.append("0");
            sb.append(hv);
        }
        return sb.toString();
    }
}
