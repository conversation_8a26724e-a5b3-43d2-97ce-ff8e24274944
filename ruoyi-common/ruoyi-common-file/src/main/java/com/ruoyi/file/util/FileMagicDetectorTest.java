package com.ruoyi.file.util;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件魔数检测测试类
 * 可通过 main 方法传入多个文件路径，输出文件魔数和识别类型
 */
public class FileMagicDetectorTest {

    public static void main(String[] args) throws Exception {
        List<String> filePaths = new ArrayList<String>();

        // 示例路径，可根据实际情况修改或从 args 接收
        filePaths.add("C:/Users/<USER>/Desktop/校园服务智能体问题汇总20250618.pdf");
        filePaths.add("C:/Users/<USER>/Desktop/语音识别demo.html");

        for (String path : filePaths) {
            try (InputStream is = new FileInputStream(path)) {
                byte[] header = new byte[16];
                int len = is.read(header);
                if (len == -1) {
                    System.out.printf("文件: %s 读取失败: 无数据%n", path);
                    continue;
                }

                String hex = bytesToHex(header);
                String type = FileMagicDetector.detectType(new FileInputStream(path));
                System.out.printf("文件: %s\n魔数: %s\n识别类型: %s\n\n", path, hex, type == null ? "未知" : type);
            } catch (Exception e) {
                System.err.printf("文件: %s 处理异常: %s%n", path, e.getMessage());
            }
        }
    }

    /**
     * 字节数组转16进制字符串（用于打印魔数）
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) sb.append("0");
            sb.append(hv);
        }
        return sb.toString();
    }
}
