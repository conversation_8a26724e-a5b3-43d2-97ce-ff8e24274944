package com.ruoyi.file.aspect;

import com.ruoyi.file.annotation.SecureFileUpload;
import com.ruoyi.file.constant.FileTypeRegistry;
import com.ruoyi.file.util.FileMagicDetector;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 上传切面：统一校验文件类型、大小、扩展名
 */
@Aspect
@Component
public class SecureUploadAspect {

    // 默认允许的扩展名列表
    private static final List<String> DEFAULT_ALLOWED_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp",
            "pdf", "docx", "xlsx", "pptx", "txt",
            "doc", "xls", "ppt",
            "zip", "7z",
            "mp3", "wav", "aac", "flac", "ogg",
            "mp4", "avi", "mov", "wmv", "mkv"
    );// "rtf", "html", "htm", "xml"

    private static final List<String> FORBIDDEN_FILENAMES = Arrays.asList(
            "crossdomain.xml", ".htaccess", "web.config", "config.php", "shell.jsp"
    );

    /**
     * 环绕处理上传方法，执行前校验文件安全性
     */
    @Around("@annotation(annotation)")
    public Object handle(ProceedingJoinPoint pjp, SecureFileUpload annotation) throws Throwable {
        List<String> allowExts = new ArrayList<>();

        if (annotation.mergeWithDefault()) {
            // 合并默认+用户指定
            allowExts.addAll(DEFAULT_ALLOWED_EXTENSIONS);
            allowExts.addAll(Arrays.asList(annotation.allowExtensions()));
        } else {
            // 覆盖模式，直接用用户指定
            allowExts.addAll(Arrays.asList(annotation.allowExtensions()));
        }

        Object[] args = pjp.getArgs();
        for (Object arg : args) {
            if (arg instanceof MultipartFile) {
                validateFile((MultipartFile) arg, allowExts, annotation.maxSizeMb());
            } else if (arg instanceof MultipartFile[]) {
                for (MultipartFile file : (MultipartFile[]) arg) {
                    validateFile(file, allowExts, annotation.maxSizeMb());
                }
            }
        }
        return pjp.proceed();
    }

    /**
     * 校验单个文件是否合法
     */
    private void validateFile(MultipartFile file, List<String> allowExts, int maxSizeMb) throws Exception {
        if (file == null || file.isEmpty() || file.getSize() == 0) {
            throw new RuntimeException("上传文件不能为空或空文件");
        }

        if (file.getSize() > maxSizeMb * 1024L * 1024L) {
            throw new RuntimeException("文件大小超出限制: " + maxSizeMb + "MB");
        }

        String ext = getExtension(file.getOriginalFilename());
        if (ext.isEmpty() || !allowExts.contains(ext)) {
            throw new RuntimeException("文件类型异常或缺少扩展名: ." + ext);
        }

        validateFileName(file.getOriginalFilename());

        // 先读bytes，防止流重复读
        byte[] fileBytes = file.getBytes();

        try (InputStream is = new ByteArrayInputStream(fileBytes)) {
            String detectedType = FileMagicDetector.detectType(is);
            if (detectedType == null || !FileTypeRegistry.TYPE_EXTENSION_MAP.containsKey(detectedType)) {
                throw new RuntimeException("无法识别的文件类型");
            }
            List<String> validExtGroup = FileTypeRegistry.TYPE_EXTENSION_MAP.get(detectedType);
            if (!validExtGroup.contains(ext)) {
                throw new RuntimeException("文件内容与扩展名不匹配");
            }
        }
    }


    private void validateTextContent(byte[] fileBytes, String ext) throws Exception {
        String content = new String(fileBytes, StandardCharsets.UTF_8);

        if ("html".equals(ext) || "htm".equals(ext)) {
            String cleaned = Jsoup.clean(content, Safelist.basic());
            if (!cleaned.equals(content)) {
                throw new RuntimeException("上传文件内容包含不安全标签");
            }
        } else if ("xml".equals(ext)) {
            if (content.toLowerCase().contains("<cross-domain-policy>")) {
                throw new RuntimeException("上传XML内容包含危险标签");
            }
        } else if ("txt".equals(ext)) {
            Pattern scriptPattern = Pattern.compile("<script\\b[^>]*>(.*?)</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            if (scriptPattern.matcher(content).find()) {
                throw new RuntimeException("上传文本内容包含脚本标签");
            }
        }
    }

    private void validateFileName(String fileName) {
        if (fileName == null) return;
        String lower = fileName.toLowerCase();
        for (String forbidden : FORBIDDEN_FILENAMES) {
            // 这里改成 contains 会更严谨防绕过
            if (lower.contains(forbidden)) {
                throw new RuntimeException("禁止上传敏感文件名：" + fileName);
            }
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) return "";
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }



}
