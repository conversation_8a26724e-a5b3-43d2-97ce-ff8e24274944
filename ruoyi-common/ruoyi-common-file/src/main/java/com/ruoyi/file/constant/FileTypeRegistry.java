package com.ruoyi.file.constant;

import java.util.*;

/**
 * 文件类型注册中心：魔数 -> 类型 -> 扩展名 映射关系
 */
public class FileTypeRegistry {

    /** 魔数 -> 文件类型 */
    public static final Map<String, String> MAGIC_TYPE_MAP = new LinkedHashMap<String, String>();

    /** 文件类型 -> 扩展名列表 */
    public static final Map<String, List<String>> TYPE_EXTENSION_MAP = new HashMap<String, List<String>>();

    static {
        // 图片类型
        MAGIC_TYPE_MAP.put("FFD8FF", "jpg");
        MAGIC_TYPE_MAP.put("89504E47", "png");
        MAGIC_TYPE_MAP.put("47494638", "gif");
        MAGIC_TYPE_MAP.put("424D", "bmp");
        MAGIC_TYPE_MAP.put("52494646", "webp");

        // 文档类型
        MAGIC_TYPE_MAP.put("25504446", "pdf");
        MAGIC_TYPE_MAP.put("504B0304", "zip");
        MAGIC_TYPE_MAP.put("7B5C727466", "rtf");
        MAGIC_TYPE_MAP.put("D0CF11E0", "doc");
        MAGIC_TYPE_MAP.put("3C3F786D6C", "xml");
        MAGIC_TYPE_MAP.put("3C68746D6C3E", "html");
        MAGIC_TYPE_MAP.put("3C21444F43545950452068746D6C", "html"); // <!DOCTYPE html>


        // 视频类型
        MAGIC_TYPE_MAP.put("00000018", "mp4");
        MAGIC_TYPE_MAP.put("52494646", "avi");
        MAGIC_TYPE_MAP.put("1A45DFA3", "mkv");

        // 音频类型
        MAGIC_TYPE_MAP.put("494433", "mp3");
        MAGIC_TYPE_MAP.put("664C6143", "flac");

        // 压缩包类型
        MAGIC_TYPE_MAP.put("377ABCAF", "7z");

        // 类型 -> 扩展名
        TYPE_EXTENSION_MAP.put("jpg", Arrays.asList("jpg", "jpeg"));
        TYPE_EXTENSION_MAP.put("png", Collections.singletonList("png"));
        TYPE_EXTENSION_MAP.put("gif", Collections.singletonList("gif"));
        TYPE_EXTENSION_MAP.put("bmp", Collections.singletonList("bmp"));
        TYPE_EXTENSION_MAP.put("webp", Collections.singletonList("webp"));

        TYPE_EXTENSION_MAP.put("pdf", Collections.singletonList("pdf"));
        TYPE_EXTENSION_MAP.put("zip", Arrays.asList("zip", "docx", "xlsx", "pptx", "odt", "ods"));
        TYPE_EXTENSION_MAP.put("7z", Collections.singletonList("7z"));
        TYPE_EXTENSION_MAP.put("rtf", Collections.singletonList("rtf"));
        TYPE_EXTENSION_MAP.put("doc", Collections.singletonList("doc"));
        TYPE_EXTENSION_MAP.put("xml", Collections.singletonList("xml"));
        TYPE_EXTENSION_MAP.put("html", Arrays.asList("html", "htm", "xhtml"));

        TYPE_EXTENSION_MAP.put("mp3", Collections.singletonList("mp3"));
        TYPE_EXTENSION_MAP.put("flac", Collections.singletonList("flac"));

        TYPE_EXTENSION_MAP.put("mp4", Collections.singletonList("mp4"));
        TYPE_EXTENSION_MAP.put("avi", Collections.singletonList("avi"));
        TYPE_EXTENSION_MAP.put("mkv", Collections.singletonList("mkv"));
    }
}
