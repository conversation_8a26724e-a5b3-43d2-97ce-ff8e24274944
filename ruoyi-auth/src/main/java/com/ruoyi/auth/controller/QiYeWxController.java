package com.ruoyi.auth.controller;


import com.ruoyi.auth.service.QiYeWxService;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/qywx")
public class QiYeWxController {

    @Resource
    private QiYeWxService qiYeWxService;
    @Resource
    private SysLoginService sysLoginService;
//	/**
//	 * @description: 获取企业微信的 access_token
//	 * @author: zhaoTianQi
//	 * @date: 2024/12/12 11:50
//	 * @param: request
//	 * @return: void
//	 **/
//	@GetMapping("/getQiWxQAccessToken")
//	public R<?> getQiWxQAccessToken() {
//		return R.ok(qiYeWxService.getQiYeWxQAccessToken());
//	}
//


    /**
     * @description: 获取企业 jsapi_ticket
     * @author: zhaoTianQi
     * @date: 2024/12/13 10:59
     * @param: code
     * @return: null
     **/
    @GetMapping("/getQiYeJsapiTicket")
    public R<?> getJsapiTicket() {
        return R.ok(qiYeWxService.getQiYeJsapiTicket());
    }

    /**
     * @description: 获取应用的 jsapi_ticket
     * @author: zhaoTianQi
     * @date: 2025/1/2 下午3:36
     * @param:
     * @return: R<?>
     **/
    @GetMapping("/getYingYongJsapiTicket")
    public R<?> getYingYongJsapiTicket() {
        return R.ok(qiYeWxService.getYingYongJsapiTicket());
    }


    /**
     * @description: 根据 url 生成授权企业签名
     * @author: zhaoTianQi
     * @date: 2024/12/17 11:08
     * @param:
     * @return: R<?>
     **/
    @GetMapping("/getQiYeSha1Signature")
    public R<?> sha1Signature(@RequestParam("url") String url) {
        return R.ok(qiYeWxService.getQiYeSha1Signature(url));
    }

    /**
     * @description: 根据 url 生成授权企 应用 签名
     * @author: zhaoTianQi
     * @date: 2025/1/2 下午3:46
     * @param: url
     * @return: R<?>
     **/
    @GetMapping("/getYingYongSha1Signature")
    public R<?> getYingYongSha1Signature(@RequestParam("url") String url) {
        return R.ok(qiYeWxService.getYingYongSha1Signature(url));
    }

    /**
     * @description: 根据 url 生成 山财企业签名
     * @author: zhaoTianQi
     * @date: 2025/2/6 09:26
     * @param:
     * @return: R<?>
     **/
    @GetMapping("/getEnterpriseSha1Signature")
    public R<?> shanCaiSha1Signature(@RequestParam("url") String url, @RequestParam("label") String label) {
        return R.ok(qiYeWxService.getEnterpriseSha1Signature(url, label));
    }

//    /**
//     * @description: 服务商 -  企业微信回调 验证URL有效性
//     * @author: zhaoTianQi
//     * @date: 2024/12/19 14:37
//     * @param: parameters
//     * @return: String
//     **/
//    @GetMapping(value = "/doValid", produces = MediaType.TEXT_PLAIN_VALUE)
//    public String doGetValid(HttpServletRequest request, HttpServletResponse response) throws AesException, UnsupportedEncodingException {
//        return qiYeWxService.doGetValid(request, response);
//    }
//
//    /**
//     * 服务商 -  企业微信数据回调
//     */
//    @PostMapping(value = "/doValid", produces = MediaType.TEXT_PLAIN_VALUE)
//    public String doPostValid(HttpServletRequest request) throws Exception {
//        return qiYeWxService.doPostValid(request);
//    }
//
//
//    /**
//     * @description: 服务商 - 根据企业微信的 code 获取登录令牌以及提示信息
//     * @author: zhaoTianQi
//     * @date: 2024/12/13 10:59
//     * @param: code
//     * @return: null
//     **/
//    @GetMapping("/getAccessToken")
//    public R<?> getAccessToken(@RequestParam("code") String code) throws InterruptedException {
//        return R.ok(qiYeWxService.getAccessToken(code));
//    }

    /**
     * @description: 企业内部自建应用 - 根据企业微信的 code 获取登录令牌以及提示信息
     * @author: zhaoTianQi
     * @date: 2024/12/13 10:59
     * @param: code
     * @param: label
     * @return: null
     **/
    @GetMapping("/getAccessTokenByInternal")
    public R<?> getAccessTokenByInternal(@RequestParam("code") String code, @RequestParam("label") String label) throws InterruptedException {
        return R.ok(qiYeWxService.getAccessTokenByInternal(code, label));
    }

//	/**
//	 * 获取服务商凭证
//	 **/
//	@PostMapping("/getProviderToken")
//	public R<?> getProviderToken() {
//		return R.ok(qiYeWxService.getProviderToken());
//	}

    //		/**
//		 * @description: 获取临时和授权码构建url
//		 * @author: zhaoTianQi
//		 * @date: 2024/12/24 22:50
//		 * @param: null
//		 * @return: null
//		 **/
//		@GetMapping("/getAuthCode")
//		public R<?> getAuthCode(HttpServletResponse response) throws IOException {
//			return R.ok(qiYeWxService.get_auth_code(response));
//		}
//
//
//
//		@GetMapping("/get_pre_auth_code")
//		public R<?> get_pre_auth_code() {
//			return R.ok(qiYeWxService.get_pre_auth_code());
//		}
//		@GetMapping("/get_suite_token")
//		public R<?> get_suite_token() {
//			return R.ok(qiYeWxService.get_suite_token());
//		}
//
    @GetMapping("/set_session_info")
    public R<?> set_session_info(@RequestParam("auth_type") String auth_type) {
        return R.ok(qiYeWxService.set_session_info(auth_type));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                String username = JwtUtils.getUserName(token);
                // 删除用户缓存记录
                AuthUtil.logoutByToken(token);
                // 记录用户退出日志
                sysLoginService.logout(username, "企业微信退出成功");
            } catch (Exception e) {
                return R.fail("token异常：" + e.getMessage());
            }
        }
        return R.ok();
    }

}
