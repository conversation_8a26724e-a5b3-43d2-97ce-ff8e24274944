package com.ruoyi.auth.controller;

import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteUserOnlineService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserOnline;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
@Slf4j
public class TokenController {
	@Resource
	private TokenService tokenService;

	@Resource
	private SysLoginService sysLoginService;
	@Resource
	private ConfigService configService;

	@Resource
	private RemoteUserOnlineService remoteUserOnlineService;

	@Resource
	private RemoteUserService remoteUserService;
	@Resource
	private RedisService redisService;


	/**
	 * web端
	 *
	 * @param form
	 * @return
	 */
	@PostMapping("login")
	public R<?> login(@RequestBody LoginBody form) {
		// 用户登录
		LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
		//判断登录人数
		String string = configService.getConfigKey2("loginNumber", SecurityConstants.INNER).get("msg").toString();
		String userWhite = configService.getConfigKey2("user_white_list", SecurityConstants.INNER).get("msg").toString();
		List<String> userWhiteList = new ArrayList<>();
		if (org.apache.commons.lang3.StringUtils.isNotBlank(userWhite)) {
			// 使用Stream API将字符串转换为List
			userWhiteList = Arrays.stream(userWhite.split(","))
					.collect(Collectors.toList());
		}
		//0则是没有人数限制
		if (!("0".equals(string)) && !(userWhiteList.contains(form.getUsername()))) {
			List<SysUserOnline> sysUserOnlines = remoteUserOnlineService.listAll(null, null, SecurityConstants.INNER);
			List<SysUserOnline> toRemove = new ArrayList<>();
			sysUserOnlines.forEach(sysUserOnline -> {
				if (sysUserOnline.getUserName().equals(form.getUsername())) {
					toRemove.add(sysUserOnline);
				}
			});
			sysUserOnlines.removeAll(toRemove);

			if ((sysUserOnlines != null) && org.apache.commons.lang3.StringUtils.isNotBlank(string)) {
				int uniqueUserCount = (int) sysUserOnlines.stream()
						.map(SysUserOnline::getUserName)
						.collect(Collectors.toCollection(HashSet::new)) // 使用HashSet自动去重
						.size();
				int loginNumber = Integer.parseInt(string);
				if ((uniqueUserCount + 1) > loginNumber) {
					return R.fail("当前访问人数较多或网络不佳，请稍后再试");
				}
			}
		}
		//体验用户校验是否过期-过期角色变为游客
		Set<String> roles = userInfo.getRoles();
		if (roles.contains("experience")) {
			SysUser sysUser = userInfo.getSysUser();
			Date expTimeEnd = sysUser.getExpTimeEnd();
			// 将 Date 转换为 Instant
			Instant instantExpTimeEnd = expTimeEnd.toInstant();
			// 获取当前时间的 Instant
			Instant now = Instant.now();
			// 比较两个时间点
			if (now.isAfter(instantExpTimeEnd)) {
				//当前时间大于 expTimeEnd，体验失效
				remoteUserService.resetUserRole(sysUser.getUserId(), SecurityConstants.INNER);
			}
		}
		// 获取登录token
		return R.ok(tokenService.createToken(userInfo));
	}

	@DeleteMapping("logout")
	public R<?> logout(HttpServletRequest request) {
		String token = SecurityUtils.getToken(request);
		if (StringUtils.isNotEmpty(token)) {
			try {
				String username = JwtUtils.getUserName(token);
				// 删除用户缓存记录
				AuthUtil.logoutByToken(token);
				// 记录用户退出日志
				sysLoginService.logout(username);
			} catch (Exception e) {
				return R.fail("token异常：" + e.getMessage());
			}
		}
		return R.ok();
	}

	@PostMapping("refresh")
	public R<?> refresh(HttpServletRequest request) {
		LoginUser loginUser = tokenService.getLoginUser(request);
		if (StringUtils.isNotNull(loginUser)) {
			// 刷新令牌有效期
			tokenService.refreshToken(loginUser);
			return R.ok();
		}
		return R.ok();
	}

	@PostMapping("register")
	public R<?> register(@RequestBody RegisterBody registerBody) {
		// 用户注册
		sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
		return R.ok();
	}

	@PostMapping("loginX")
	public R<?> loginX(@RequestBody LoginBody form) {
		// 用户登录
		LoginUser userInfo = sysLoginService.loginX(form.getUsername(), form.getCode());
		// 获取登录token
		return R.ok(tokenService.createToken(userInfo));
	}

	/**
	 * @description: 手机号 验证码 登录
	 * @author: zhaoTianQi 
	 * @date: 2025/1/3 上午11:16
	 * @param: form
	 * @return: R<?>
	 **/
	@PostMapping("/loginByPhoneNumber")
	public R<?> loginByPhoneNumber(@RequestBody LoginBody form) {
		// 用户登录
		LoginUser userInfo = sysLoginService.loginByPhoneNumber(form.getPhoneNumber(), form.getCode());
		// 获取登录token
		Map<String, Object> tokenMap = tokenService.createToken(userInfo);
		tokenMap.put("userInfo", userInfo);
		return R.ok(tokenMap);
	}

	/**
	 * 小程序
	 *
	 * @param form
	 * @return
	 */
	@PostMapping("loginS")
	public R<?> loginS(@RequestBody LoginBody form) {
		// 用户登录
		LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
		//判断登录人数
		String string = configService.getConfigKey2("loginNumber", SecurityConstants.INNER).get("msg").toString();
		String userWhite = configService.getConfigKey2("user_white_list", SecurityConstants.INNER).get("msg").toString();
		List<String> userWhiteList = new ArrayList<>();
		if (org.apache.commons.lang3.StringUtils.isNotBlank(userWhite)) {
			// 使用Stream API将字符串转换为List
			userWhiteList = Arrays.stream(userWhite.split(","))
					.collect(Collectors.toList());
		}
		//0则是没有人数限制
		if (!("0".equals(string)) && !(userWhiteList.contains(form.getUsername()))) {
			List<SysUserOnline> sysUserOnlines = remoteUserOnlineService.listAll(null, null, SecurityConstants.INNER);
			List<SysUserOnline> toRemove = new ArrayList<>();
			sysUserOnlines.forEach(sysUserOnline -> {
				if (sysUserOnline.getUserName().equals(form.getUsername())) {
					toRemove.add(sysUserOnline);
				}
			});
			sysUserOnlines.removeAll(toRemove);

			if ((sysUserOnlines != null) && org.apache.commons.lang3.StringUtils.isNotBlank(string)) {
				int uniqueUserCount = (int) sysUserOnlines.stream()
						.map(SysUserOnline::getUserName)
						.collect(Collectors.toCollection(HashSet::new)) // 使用HashSet自动去重
						.size();
				int loginNumber = Integer.parseInt(string);
				if ((uniqueUserCount + 1) > loginNumber) {
					return R.fail("当前访问人数较多或网络不佳，请稍后再试");
				}
			}
		}
		//体验用户校验是否过期-过期角色变为游客
		Set<String> roles = userInfo.getRoles();
		if (roles.contains("experience")) {
			SysUser sysUser = userInfo.getSysUser();
			Date expTimeEnd = sysUser.getExpTimeEnd();
			// 将 Date 转换为 Instant
			Instant instantExpTimeEnd = expTimeEnd.toInstant();
			// 获取当前时间的 Instant
			Instant now = Instant.now();
			// 比较两个时间点
			if (now.isAfter(instantExpTimeEnd)) {
				//当前时间大于 expTimeEnd，体验失效
				remoteUserService.resetUserRole(sysUser.getUserId(), SecurityConstants.INNER);
			}
		}
		// 获取登录token
		Map<String, Object> tokenMap = tokenService.createToken(userInfo);
		tokenMap.put("userInfo", userInfo);
		return R.ok(tokenMap);
	}

	@PostMapping("registerS")
	public R<?> registerS(@RequestBody RegisterBody registerBody) {
		// 用户注册
		sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
		return R.ok();
	}

	/**
	 * @description: 单点登录外部访问传递userCode
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 11:10
	 * @param: loginBody
	 * @return: R<?>
	 **/
	@CrossOrigin(origins = "*") // 允许所有来源跨域
	@GetMapping("/externalAccess")
	public R<?> externalAccess(LoginBody loginBody) throws NoSuchAlgorithmException, InvalidKeySpecException {
		LoginUser loginUser = sysLoginService.externalAccess(loginBody);
		Map<String, Object> map = tokenService.createToken(loginUser);
		String redirectUrlCatch = Convert.toStr(redisService.getCacheObject(Constants.REDIRECTURL));

		if (StringUtils.isBlank(redirectUrlCatch)) {
			throw new ServiceException("没有配置参数" + Constants.REDIRECTURL);
		}

		String redirectUrl = redirectUrlCatch + "?access_token=" + map.get("access_token") + "&expires_in=" + map.get("expires_in");
		map.put("redirect", redirectUrl);
		return R.ok(map);
	}

//	/**
//	 * cas 单点登录
//	 *
//	 * @param request 请求头(姓名+身份证号)
//	 * @param ticket cas 票据
//	 * @return
//	 */
//	@GetMapping(value = "/api/loginByNameAndCardNo")
//	@ApiOperation("cas单点登录")
//	public String loginByNameAndCardNo(HttpServletRequest request) {
//		CasUserInfo userInfo = CasUtil.getCasUserInfoFromCas(request);
//		log.info("userInfo = " + JSONObject.toJSON(userInfo));
//		String url = "main";
//		// 登录用户校验 
//		// xxxxx
//		// 用户数据为 true
//		// 跳转页面
//		return "url";
//	}

//	@GetMapping("/casLogin")
//	public R<?> casLogin(LoginBody loginBody, HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
//		log.info("进入 casLogin 方法，准备处理请求");
//		// 获取用户信息
//		R<LoginUser> userResult = remoteUserService.getUserByUserCode(loginBody.getUserCode());
//		Map<String, Object> map = new HashMap<>();
//		String msg = null;
//		// 检查用户是否存在
//		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
//			msg = "身份(学号或者工号)为:" + loginBody.getUserCode() + " 的用户不存在";
//		}else {
//			LoginUser userInfo = userResult.getData();
//			map = tokenService.createToken(userInfo);
//		}
//		return R.ok(map,msg);
//
//	}


	/**
	 * @description: 通过userCode获取token以及相关数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/4 16:24
	 * @param: loginBody
	 * @return: R<?>
	 **/
	@PostMapping("/getTokenByUserCode")
	public R<?> getTokenByUserCode(@RequestBody LoginBody loginBody,HttpServletRequest request){
		return R.ok(sysLoginService.getTokenByUserCode(loginBody,request));
	}

	/**
	 * 获取userCode并重定向
	 *
	 * @param request  请求
	 * @param response 响应
	 **/
	@CrossOrigin(origins = "*")
	@GetMapping("/casLogin")
	public void casLogin(HttpServletRequest request, HttpServletResponse response) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {

		sysLoginService.redirectToSsoLoginPage(request, response);

//		// 获取请求的 URI
//		String requestUri = request.getRequestURI();
//		log.info("Request URI: {}", requestUri);
//
//		// 获取请求参数
//		Map<String, String[]> parameters = request.getParameterMap();
//
//		// 打印请求参数
//		if (parameters != null && !parameters.isEmpty()) {
//			log.info("Request Parameters: ");
//			for (Map.Entry<String, String[]> entry : parameters.entrySet()) {
//				String paramName = entry.getKey();
//				String[] paramValues = entry.getValue();
//				for (String value : paramValues) {
//					log.info("{} = {}", paramName, value);
//				}
//			}
//		} else {
//			log.info("No request parameters found.");
//		}
//
//		// 获取请求头并打印
//		Enumeration<String> headerNames = request.getHeaderNames();
//		while (headerNames.hasMoreElements()) {
//			String headerName = headerNames.nextElement();
//			String headerValue = request.getHeader(headerName);
//			log.info("Header: {} = {}", headerName, headerValue);
//		}
//
//		String uid = request.getRemoteUser();
//		log.info("uid: {}", uid);
//		String cn = "";
//		Principal principal = request.getUserPrincipal();
//		log.info("principal: {}", principal);
//
//		if (principal != null && principal instanceof AttributePrincipal) {
//			AttributePrincipal aPrincipal = (AttributePrincipal) principal;
//			//获取用户信息中公开的Attributes部分
//			Map<String, Object> map = aPrincipal.getAttributes();
//			log.info("map: {}", map);
//			// 获取姓名,可以根据属性名称获取其他属性
//			cn = (String) map.get("cn");

//		String username = request.getRemoteUser();
//		HttpSession httpSession = request.getSession();
//
//		String url = request.getParameter("redirect");
//		R<LoginUser> userResult = remoteUserService.getUserByUserCode(username);
//		// 检查用户是否存在
//		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
//			throw new ServiceException("身份(学号或者工号)为:" + username + " 的用户不存在");
//		}
//		LoginUser userInfo = userResult.getData();
//		Map<String, Object> token = tokenService.createToken(userInfo);
//
//		Cookie tokenCookie = new Cookie(CasProperties.WEB_TOKEN_KEY, (String) token.get("access_token"));
//		//必须设置path,否则获取不到cookie
//		tokenCookie.setPath("/");
//		response.addCookie(tokenCookie);
//		Cookie expiresCookie = new Cookie(CasProperties.WEB_TOKEN_EXPIRES, ((Long) token.get("expires_in")).toString());
//		expiresCookie.setPath("/");
//		response.addCookie(expiresCookie);
//		//设置后端认证成功标识
//
//		httpSession.setAttribute(CasProperties.CAS_TOKEN, token.get("access_token"));
//		//登录成功后跳转到前端访问页面
//		response.sendRedirect(url);
	}

	@CrossOrigin(origins = "*")
	@GetMapping("/casLogout")
	public R<?> casLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
		return R.ok(sysLoginService.casLogout(request, response));
	}
}
