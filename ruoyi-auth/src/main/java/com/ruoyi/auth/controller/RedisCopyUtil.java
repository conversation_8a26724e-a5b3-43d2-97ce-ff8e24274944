package com.ruoyi.auth.controller;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.resps.Tuple;

import java.util.*;

public class RedisCopyUtil {

	// 源Redis配置（线上）
	private static final String SOURCE_HOST = "************";
	private static final int SOURCE_PORT = 6380;
	private static final String SOURCE_PASSWORD = "root@123";

	// 目标Redis配置（本地）
	private static final String TARGET_HOST = "localhost";
	private static final int TARGET_PORT = 6379;
	private static final String TARGET_PASSWORD = "";

	public static void copyRedisData() {
		Jedis sourceJedis = null;
		Jedis targetJedis = null;

		try {
			// 连接源Redis
			System.out.println("连接到源Redis服务器: " + SOURCE_HOST + ":" + SOURCE_PORT);
			sourceJedis = new Jedis(SOURCE_HOST, SOURCE_PORT);
			if (SOURCE_PASSWORD != null && !SOURCE_PASSWORD.isEmpty()) {
				sourceJedis.auth(SOURCE_PASSWORD);
				System.out.println("已使用密码进行身份验证.");
			}

			// 连接目标Redis
			System.out.println("连接到目标Redis服务器: " + TARGET_HOST + ":" + TARGET_PORT);
			targetJedis = new Jedis(TARGET_HOST, TARGET_PORT);
			if (TARGET_PASSWORD != null && !TARGET_PASSWORD.isEmpty()) {
				targetJedis.auth(TARGET_PASSWORD);
				System.out.println("已使用密码进行身份验证.");
			}

//			// 获取源Redis的所有键
//			System.out.println("获取源Redis的所有键...");
//			Set<String> keys = sourceJedis.keys("*");
//			System.out.println("共找到 " + keys.size() + " 个键需要复制.");

			// 获取以 "qy_wx" 开头的所有键
			System.out.println("获取源Redis的以 'qy_wx' 开头的所有键...");
			Set<String> keys = sourceJedis.keys("qy_wx*");
			System.out.println("共找到 " + keys.size() + " 个以 'qy_wx' 开头的键需要复制.");

			
			// 复制每个键值对
			for (String key : keys) {
				System.out.println("开始复制键: " + key);

				String type = sourceJedis.type(key);
				System.out.println("键 " + key + " 的类型: " + type);

				switch (type) {
					case "string":
						targetJedis.set(key, sourceJedis.get(key));
						System.out.println("复制键 " + key + " 的字符串值.");
						break;
					case "list":
						List<String> list = sourceJedis.lrange(key, 0, -1);
						for (String value : list) {
							targetJedis.rpush(key, value);
						}
						System.out.println("复制键 " + key + " 的列表值.");
						break;
					case "set":
						Set<String> set = sourceJedis.smembers(key);
						for (String value : set) {
							targetJedis.sadd(key, value);
						}
						System.out.println("复制键 " + key + " 的集合值.");
						break;
					case "hash":
						Map<String, String> hash = sourceJedis.hgetAll(key);
						targetJedis.hmset(key, hash);
						System.out.println("复制键 " + key + " 的哈希值.");
						break;
					case "zset":
						Set<Tuple> zset = (Set<Tuple>) sourceJedis.zrangeWithScores(key, 0, -1);
						for (Tuple tuple : zset) {
							targetJedis.zadd(key, tuple.getScore(), tuple.getElement());
						}
						System.out.println("复制键 " + key + " 的有序集合值.");
						break;
					default:
						System.out.println("无法识别的键类型: " + key);
						break;
				}

				// 复制过期时间
				Long ttl = sourceJedis.ttl(key);
				if (ttl > 0) {
					targetJedis.expire(key, ttl.intValue());
					System.out.println("键 " + key + " 的过期时间也已复制: " + ttl + " 秒.");
				} else {
					System.out.println("键 " + key + " 没有过期时间.");
				}
			}

			System.out.println("所有数据已复制完毕!");

		} catch (Exception e) {
			System.err.println("复制过程中发生错误: " + e.getMessage());
			e.printStackTrace();
		} finally {
			// 关闭连接
			if (sourceJedis != null) {
				sourceJedis.close();
				System.out.println("源Redis连接已关闭.");
			}
			if (targetJedis != null) {
				targetJedis.close();
				System.out.println("目标Redis连接已关闭.");
			}
		}
	}

	public static void main(String[] args) {
		copyRedisData();
	}
}
