package com.ruoyi.auth.form;

import lombok.Data;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Data
public class LoginBody
{
    /**
     * 用户名
     */
    private String username;

	/**
	 * 手机号
	 */
	private String phoneNumber;

    /**
     * 用户密码
     */
    private String password;

    private String code;

    /** 身份代码 学生学号student_id或者教师工号job_id**/
    private String userCode;

    /** 旧的 token */
    private String oldAccessToken;

    private String keyId;

    private Boolean rememberMe; // 新增字段

    // 新增 getter 和 setter
    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }
}
