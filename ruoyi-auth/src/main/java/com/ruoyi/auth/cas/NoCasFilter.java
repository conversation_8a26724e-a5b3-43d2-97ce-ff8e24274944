package com.ruoyi.auth.cas;
 
import javax.servlet.*;
import java.io.IOException;
 
/**
 * <AUTHOR>
 * @className: NoCasFilter
 * @projectName RuoYi-Cloud-master
 * @description: 单点登录停用辅助过滤器
 * @date 2022/7/21 11:19
 **/
public final class NoCasFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        filterChain.doFilter(request, response);
    }
}