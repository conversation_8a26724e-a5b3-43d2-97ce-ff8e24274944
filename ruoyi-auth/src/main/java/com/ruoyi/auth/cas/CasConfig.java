package com.ruoyi.auth.cas;


import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.authentication.AuthenticationFilter;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CharacterEncodingFilter;

import javax.annotation.Resource;
import javax.servlet.Filter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className: CasConfig
 * @projectName RuoYi-Cloud-master
 * @description: TODO
 * @date 2022/7/19 14:17
 **/
@Configuration
@Slf4j
public class CasConfig {

	@Resource
	private CasProperties casProperties;

	@Resource
	private CustomSessionMappingStorage customSessionMappingStorage;

	@Bean
	public FilterRegistrationBean<CharacterEncodingFilter> customCharacterEncodingFilter() {
		log.info("自定义字符集过滤器 customCharacterEncodingFilter");
		FilterRegistrationBean<CharacterEncodingFilter> filterRegistrationBean = new FilterRegistrationBean<>();
		CharacterEncodingFilter filter = new CharacterEncodingFilter();
		filter.setEncoding("UTF-8");
		filter.setForceEncoding(true); // 强制使用UTF-8编码
		filterRegistrationBean.setFilter(filter);
		filterRegistrationBean.addUrlPatterns("/*"); // 对所有请求生效
		filterRegistrationBean.setOrder(1); // 设置过滤器执行顺序
		return filterRegistrationBean;
	}


	/**
	 * 单点登出过滤器
	 *
	 **/
	@Bean
	public FilterRegistrationBean<Filter> logoutFilter() {
		log.info("单点登出过滤器 logoutFilter");
		FilterRegistrationBean<Filter> authenticationFilter = new FilterRegistrationBean<>();
		SingleSignOutFilter signOutFilter = new SingleSignOutFilter();
		signOutFilter.setSessionMappingStorage(customSessionMappingStorage);
		signOutFilter.setIgnoreInitConfiguration(true);
		authenticationFilter.setFilter(signOutFilter);
		authenticationFilter.addUrlPatterns("/*");
		Map<String, String> initParameters = new HashMap<>();
		initParameters.put("casServerUrlPrefix", casProperties.getCasServerUrlPrefix());
		authenticationFilter.setInitParameters(initParameters);
		authenticationFilter.setOrder(1);
		if (casProperties.getEnabled()) {
			return authenticationFilter;
		}
		return new FilterRegistrationBean<>(new NoCasFilter());
	}

	/**
	 * 单点登录认证入口
	 *
	 **/
	@Bean
	public FilterRegistrationBean<Filter> authenticationFilterRegistrationBean() {
        log.info("单点登录认证入口 authenticationFilterRegistrationBean");
		FilterRegistrationBean<Filter> authenticationFilter = new FilterRegistrationBean<>();
		authenticationFilter.setFilter(new AuthenticationFilter());
		Map<String, String> initParameters = new HashMap<>();
		initParameters.put("casServerLoginUrl", casProperties.getCasServerLoginUrl());
		initParameters.put("serverName", casProperties.getClientUrl());
		authenticationFilter.setInitParameters(initParameters);
		authenticationFilter.setOrder(2);
		List<String> urlPatterns = new ArrayList<>();
		urlPatterns.add("/casLogin");
		authenticationFilter.setUrlPatterns(urlPatterns);
		if (casProperties.getEnabled()) {
			return authenticationFilter;
		}
		return new FilterRegistrationBean<>(new NoCasFilter());
	}

	/**
	 * 单点登录验证入口
	 *
	 **/
	@Bean
	public FilterRegistrationBean<Filter> validationFilterRegistrationBean() {
        log.info(" 单点登录验证入口 validationFilterRegistrationBean");
		FilterRegistrationBean<Filter> authenticationFilter = new FilterRegistrationBean<>();
		authenticationFilter.setFilter(new Cas20ProxyReceivingTicketValidationFilter());
		Map<String, String> initParameters = new HashMap<>();
		initParameters.put("casServerUrlPrefix", casProperties.getCasServerUrlPrefix());
		initParameters.put("serverName", casProperties.getClientUrl());
		initParameters.put("encoding", "UTF-8");
		initParameters.put("useSession", "true");
		authenticationFilter.setInitParameters(initParameters);
		authenticationFilter.setOrder(3);
		List<String> urlPatterns = new ArrayList<>();
		urlPatterns.add("/*");
		authenticationFilter.setUrlPatterns(urlPatterns);
		if (casProperties.getEnabled()) {
			return authenticationFilter;
		}
		return new FilterRegistrationBean<>(new NoCasFilter());
	}

	/**
	 * 单点登录获取登录信息
	 *
	 **/
	@Bean
	public FilterRegistrationBean<Filter> casHttpServletRequestWrapperFilter() {
        log.info("单点登录获取登录信息 casHttpServletRequestWrapperFilter");
		FilterRegistrationBean<Filter> authenticationFilter = new FilterRegistrationBean<>();
		authenticationFilter.setFilter(new HttpServletRequestWrapperFilter());
		authenticationFilter.setOrder(4);
		List<String> urlPatterns = new ArrayList<>();
		urlPatterns.add("/*");
		authenticationFilter.setUrlPatterns(urlPatterns);
		if (casProperties.getEnabled()) {
			return authenticationFilter;
		}
		return new FilterRegistrationBean<>(new NoCasFilter());
	}


}