package com.ruoyi.auth.cas;
 
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
 
/**
 * <AUTHOR>
 * @className: CasProperty
 * @projectName RuoYi-Cloud-master
 * @description: cas配置参数
 * @date 2022/7/21 10:11
 **/
@Configuration
@RefreshScope
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CasProperties {
 
    @Value("${cas.enable}")
    private Boolean enabled;
 
    @Value("${cas.server-url-prefix}")
    private String casServerUrlPrefix;
 
    @Value("${cas.server-login}")
    private String casServerLoginUrl;
 
    @Value("${cas.client-url}")
    private String clientUrl;


    /**
     * CAS登录成功后的后台标识
     **/
    public static final String CAS_TOKEN = "cas_token";

    /**
     * CAS登录成功后的前台Cookie的Key
     **/
    public static final String WEB_TOKEN_KEY = "Cloud-Token";

    /**
     * CAS登录成功后的前台Cookie的Expires-In
     **/
    public static final String WEB_TOKEN_EXPIRES = "Cloud-Expires-In";
}