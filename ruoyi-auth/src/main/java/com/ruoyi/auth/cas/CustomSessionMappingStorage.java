package com.ruoyi.auth.cas;


import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.session.StandardSessionFacade;
import org.jasig.cas.client.session.SessionMappingStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className: CustomSessionMappingStorage
 * @projectName RuoYi-Vue-master
 * @description: 单点登录-前后端分离-单点登出删除token
 * @date 2022/4/28 12:56
 **/
@Component
@Slf4j
public class CustomSessionMappingStorage implements SessionMappingStorage {
    private final Map<String, HttpSession> MANAGED_SESSIONS = new HashMap();
    private final Map<String, String> ID_TO_SESSION_KEY_MAPPING = new HashMap();
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SysLoginService sysLoginService;

    public CustomSessionMappingStorage() {
    }

    @Override
    public synchronized void addSessionById(String mappingId, HttpSession session) {
        this.ID_TO_SESSION_KEY_MAPPING.put(session.getId(), mappingId);
        this.MANAGED_SESSIONS.put(mappingId, session);
    }

    @Override
    public synchronized void removeBySessionById(String sessionId) {
        logger.info("Attempting to remove Session=[{}]", sessionId);
        String key = (String) this.ID_TO_SESSION_KEY_MAPPING.get(sessionId);
        if (this.logger.isDebugEnabled()) {
            if (key != null) {
                logger.info("Found mapping for session.  Session Removed.");
            } else {
                logger.info("No mapping for session found.  Ignoring.");
            }
        }

        this.MANAGED_SESSIONS.remove(key);
        this.ID_TO_SESSION_KEY_MAPPING.remove(sessionId);
    }

    @Override
    public synchronized HttpSession removeSessionByMappingId(String mappingId) {
        StandardSessionFacade session = (StandardSessionFacade) this.MANAGED_SESSIONS.get(mappingId);
        if (session != null) {
            this.removeBySessionById(session.getId());
            logger.info("用户会话已清除，session={}", session);
            try {
                String token = (String) session.getAttribute(CasProperties.CAS_TOKEN);
                if (StringUtils.isNotEmpty(token)) {
                    try {
                        String username = JwtUtils.getUserName(token);
                        // 删除用户缓存记录
                        AuthUtil.logoutByToken(token);
                        // 记录用户退出日志
                        sysLoginService.logout(username, "CAS退出");
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            } catch (IllegalStateException e) {
                logger.info("已成功登出");
            }
        }
        return session;
    }
}