package com.ruoyi.auth.service;

import com.ruoyi.auth.form.LoginBody;

import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.RSAUtils;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.RemoteUserOnlineService;
import com.ruoyi.system.api.domain.SysDictData;

import com.ruoyi.system.api.domain.SysUserOnline;

import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.RSAUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.RemoteUserOnlineService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserOnline;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.Principal;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysLoginService {
	@Resource
	private RemoteUserService remoteUserService;

	@Resource
	private SysPasswordService passwordService;

	@Resource
	private SysRecordLogService recordLogService;

	@Resource
	private RedisService redisService;

	@Resource
	private ConfigService configService;

	@Resource
	private RemoteUserOnlineService remoteUserOnlineService;
	@Resource
	private TokenService tokenService;
	@Resource
	private RemoteDictTypeService remoteDictTypeService;

	@Resource
	@Lazy
	private SysLoginService sysLoginService;


	/**
	 * 登录
	 */
	public LoginUser login(String username, String password) {
		// 用户名或密码为空 错误
		if (StringUtils.isAnyBlank(username, password)) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
			throw new ServiceException("用户/密码必须填写");
		}
		// 密码如果不在指定范围内 错误
		if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
				|| password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
			throw new ServiceException("用户密码不在指定范围");
		}
		// 用户名不在指定范围内 错误
		if (username.length() < UserConstants.USERNAME_MIN_LENGTH
				|| username.length() > UserConstants.USERNAME_MAX_LENGTH) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
			throw new ServiceException("用户名不在指定范围");
		}
		// IP黑名单校验
		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
			throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
		}
		// 查询用户信息
		R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
			throw new ServiceException("登录用户：" + username + " 不存在");
		}

		if (R.FAIL == userResult.getCode()) {
			throw new ServiceException(userResult.getMsg());
		}

		LoginUser userInfo = userResult.getData();
		SysUser user = userResult.getData().getSysUser();
		if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
			throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
		}
		if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
			throw new ServiceException("对不起，您的账号：" + username + " 已停用");
		}
		passwordService.validate(user, password);
		recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
		return userInfo;
	}

	public void logout(String loginName) {
		recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
	}

	public void logout(String loginName, String msg) {
		recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "msg");
	}

	/**
	 * 注册
	 */
	public void register(String username, String password) {
		// 用户名或密码为空 错误
		if (StringUtils.isAnyBlank(username, password)) {
			throw new ServiceException("用户/密码必须填写");
		}
		if (username.length() < UserConstants.USERNAME_MIN_LENGTH
				|| username.length() > UserConstants.USERNAME_MAX_LENGTH) {
			throw new ServiceException("账户长度必须在2到20个字符之间");
		}
		if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
				|| password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
			throw new ServiceException("密码长度必须在5到20个字符之间");
		}

		// 注册用户信息
		SysUser sysUser = new SysUser();
		sysUser.setUserName(username);
//        sysUser.setNickName("");
		sysUser.setPassword(SecurityUtils.encryptPassword(password));
		R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);
		if (R.FAIL == registerResult.getCode()) {
			throw new ServiceException(registerResult.getMsg());
		}
		recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
	}

	public LoginUser loginX(String username, String code) {
		// 用户名或密码为空 错误
		if (StringUtils.isAnyBlank(username, code)) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/验证码必须填写");
			throw new ServiceException("用户/验证码必须填写");
		}
		if (!redisService.hasKey("DL-" + username)) {
			throw new ServiceException("验证码失效或未发送");
		}
		String cacheObject = redisService.getCacheObject("DL-" + username).toString();
		if (!code.equals(cacheObject)) {
			throw new ServiceException("验证码错误");
		}

		// IP黑名单校验
		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
			throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
		}
		// 查询用户信息
		R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
			throw new ServiceException("登录用户：" + username + " 不存在");
		}

		if (R.FAIL == userResult.getCode()) {
			throw new ServiceException(userResult.getMsg());
		}

		LoginUser userInfo = userResult.getData();
		SysUser user = userResult.getData().getSysUser();
		if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
			throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
		}
		if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
			recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
			throw new ServiceException("对不起，您的账号：" + username + " 已停用");
		}

		recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
		return userInfo;
	}

	/**
	 * @description: 外部访问传递userCode
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 11:12
	 * @param: loginBody
	 * @return: LoginUser
	 **/
	public LoginUser externalAccess(LoginBody loginBody) {
		if (StringUtils.isBlank(loginBody.getUserCode())) {
			recordLogService.recordLogininfor(loginBody.getUserCode(), Constants.LOGIN_FAIL, "用户/密码必须填写");
			throw new ServiceException("身份代码 getUserCode 为空");
		}
//		String PRIVATE = Convert.toStr(redisService.getCacheObject(Constants.SSO_LOGIN_PRIVATEKEY));
//		if (StringUtils.isBlank(PRIVATE)) {
//			throw new ServiceException("没有配置私钥参数" + Constants.SSO_LOGIN_PRIVATEKEY);
//		}
//		try {
//			// 将 Base64 解密的私钥转换为对象
//			RSAPrivateKey privateKey = RSAUtils.getPrivateKey(PRIVATE);
//			// 使用私钥解密用户传递的数据
//			String decryptedData = RSAUtils.privateDecrypt(loginBody.getUserCode(), privateKey);
//
//			loginBody.setUserCode(loginBody.getUserCode());
//		} catch (Exception e) {
//			throw new ServiceException("身份代码解密失败");
//		}
		loginBody.setUserCode(loginBody.getUserCode());

		// 获取用户信息
		R<LoginUser> userResult = remoteUserService.getUserByUserCode(loginBody.getUserCode());

		// 检查用户是否存在
		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
			recordLogService.recordLogininfor(loginBody.getUserCode(), Constants.LOGIN_FAIL, "外部系统访问-登录用户不存在");
			throw new ServiceException("身份(学号或者工号)为:" + loginBody.getUserCode() + " 的用户不存在");
		}
		LoginUser userInfo = userResult.getData();
		SysUser sysUser = userResult.getData().getSysUser();

		if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "外部系统访问-对不起，您的账号已被删除");
			throw new ServiceException("对不起，您的账号：" + sysUser.getUserName() + " 已被删除");
		}
		if (UserStatus.DISABLE.getCode().equals(sysUser.getUserName())) {
			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "外部系统访问-用户已停用，请联系管理员");
			throw new ServiceException("对不起，您的账号：" + sysUser.getUserName() + " 已停用");
		}
		// 检查IP黑名单
		checkIpBlacklist(sysUser.getUserName());
		// 检查登录人数限制
		checkLoginLimit(sysUser);


		//  检查体验用户校验是否过期-过期角色变为游客
		checkUserTrialExpiration(userInfo.getRoles(), sysUser);

		recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS, "外部系统访问成功");


		return userInfo;
	}

	/**
	 * @description: IP黑名单校验
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 14:45
	 * @param: userName
	 * @return: void
	 **/
	private void checkIpBlacklist(String userName) {
		// IP黑名单校验
		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
			recordLogService.recordLogininfor(userName, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
			throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
		}
	}


	/**
	 * @description: 检查体验用户校验是否过期-过期角色变为游客
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 14:42
	 * @param: roleKeys
	 * @param: sysUser
	 * @return: void
	 **/
	private void checkUserTrialExpiration(Set<String> roleKeys, SysUser sysUser) {
		//体验用户校验是否过期-过期角色变为游客
		if (roleKeys.contains("experience")) {
			Date expTimeEnd = sysUser.getExpTimeEnd();
			// 将 Date 转换为 Instant
			Instant instantExpTimeEnd = expTimeEnd.toInstant();
			// 获取当前时间的 Instant
			Instant now = Instant.now();
			// 比较两个时间点
			if (now.isAfter(instantExpTimeEnd)) {
				//当前时间大于 expTimeEnd，体验失效
				remoteUserService.resetUserRole(sysUser.getUserId(), SecurityConstants.INNER);
			}
		}
	}

	/**
	 * @description: 检查登录人数限制
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 14:42
	 * @param: sysUser
	 * @return: void
	 **/
	private void checkLoginLimit(SysUser sysUser) {
		String loginNumberStr = configService.getConfigKey2("loginNumber", SecurityConstants.INNER).get("msg").toString();
		String userWhite = configService.getConfigKey2("user_white_list", SecurityConstants.INNER).get("msg").toString();
		List<String> userWhiteList = new ArrayList<>();
		if (StringUtils.isNotBlank(userWhite)) {
			// 使用Stream API将字符串转换为List
			userWhiteList = Arrays.stream(userWhite.split(","))
					.collect(Collectors.toList());
		}
		//0则是没有人数限制
		if (!("0".equals(loginNumberStr)) && !(userWhiteList.contains(sysUser.getUserName()))) {
			List<SysUserOnline> sysUserOnlines = remoteUserOnlineService.listAll(null, null, SecurityConstants.INNER);
			List<SysUserOnline> toRemove = new ArrayList<>();
			sysUserOnlines.forEach(sysUserOnline -> {
				if (sysUserOnline.getUserName().equals(sysUser.getUserName())) {
					toRemove.add(sysUserOnline);
				}
			});
			sysUserOnlines.removeAll(toRemove);

			if (StringUtils.isNotBlank(loginNumberStr)) {
				int uniqueUserCount = (int) sysUserOnlines.stream()
						.map(SysUserOnline::getUserName)
						.collect(Collectors.toCollection(HashSet::new)) // 使用HashSet自动去重
						.size();
				int loginNumber = Integer.parseInt(loginNumberStr);
				if ((uniqueUserCount + 1) > loginNumber) {
					throw new ServiceException("当前访问人数较多或网络不佳，请稍后再试");
				}
			}
		}
	}

	public List<Object> casLogin(LoginBody loginBody) {
		List<Object> dataList = new ArrayList<>(2);
		Map<String, Object> map = new HashMap<>();
		String msg = "";
		List<SysDictData> sysDictDatas = remoteDictTypeService.dictTypeGetInfo(Constants.SSO_LOGIN_PARAMETERS, SecurityConstants.INNER);
		if (sysDictDatas.isEmpty()) {
			msg = "【字典数据异常" + Constants.SSO_LOGIN_PARAMETERS + "没有配置】";
		}
		// 使用 Stream API 将 List 转换为 Map
		Map<String, SysDictData> dictDataMap = sysDictDatas.stream()
				.collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
		SysDictData sysDictData = dictDataMap.get(Constants.SHANCAILOGINREDIRECTURL);
		if (StringUtils.isNull(sysDictData)) {
			msg += "【字典数据异常" + Constants.SHANCAILOGINREDIRECTURL + "没有配置】";
		}
		map.put(Constants.SHANCAILOGINREDIRECTURL, sysDictData.getDictValue());
		sysDictData = dictDataMap.get(Constants.AICAILOGINURL);
		if (StringUtils.isNull(sysDictData)) {
			msg += "【字典数据异常" + Constants.AICAILOGINURL + "没有配置】";
		}
		map.put(Constants.AICAILOGINURL, sysDictData.getDictValue());

		R<LoginUser> userResult = remoteUserService.getUserByUserCode(loginBody.getUserCode());

		// 检查用户是否存在
		if (StringUtils.isBlank(loginBody.getUserCode())) {
			msg += "【参数UserCode异常】";
		} else if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
			msg += "【身份(学号或者工号)为:" + loginBody.getUserCode() + " 的用户不存在】";
		} else {
			LoginUser userInfo = userResult.getData();
			map = tokenService.createToken(userInfo);
		}

		dataList.add(map);
		dataList.add(msg);
		return dataList;
	}

	public void redirectToSsoLoginPage(HttpServletRequest request, HttpServletResponse response) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {
		// CAS 服务认证后带来的数据
		log.info("开始进行CAS服务认证 完成登录");
		String uid = request.getRemoteUser();

		Principal principal = request.getUserPrincipal();
		if (principal instanceof AttributePrincipal) {
			AttributePrincipal aPrincipal = (AttributePrincipal) principal;
			//获取用户信息中公开的Attributes部分
			Map<String, Object> map = aPrincipal.getAttributes();
			// 获取姓名,可以根据属性名称获取其他属性
			log.info("获取用户信息中公开的Attributes部分{}", map);
		}

		List<SysDictData> sysDictDatas = remoteDictTypeService.dictTypeGetInfo(Constants.SSO_LOGIN_PARAMETERS, SecurityConstants.INNER);
		if (sysDictDatas.isEmpty()) {
			throw new ServiceException("【字典数据异常" + Constants.SSO_LOGIN_PARAMETERS + "没有配置】");
		}
		// 使用 Stream API 将 List 转换为 Map
		Map<String, SysDictData> dictDataMap = sysDictDatas.stream()
				.collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
		SysDictData sysDictData = dictDataMap.get(Constants.AICAICASLOGINURL);
		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.AICAICASLOGINURL + "没有配置】");
		}
		String rsaPublicKey = Convert.toStr(redisService.getCacheObject(Constants.RSA_PUBLICKEY));
		if (StringUtils.isBlank(rsaPublicKey)) {
			throw new ServiceException("【rsa-publicKey 公钥没有配置】");
		}

		// 解析公钥字符串为 RSAPublicKey 对象
		RSAPublicKey publicKey = RSAUtils.getPublicKey(rsaPublicKey);
		// 使用公钥进行加密
		String encryptedUid = RSAUtils.publicEncrypt(uid, publicKey);
		log.info("完成加密 {},==》{}", uid, encryptedUid);
		log.info("【backUrl】{}?userCode={}", sysDictData.getDictValue(), encryptedUid);
		String backUrl = sysDictData.getDictValue() + "?userCode=" + encryptedUid;
		response.sendRedirect(backUrl);
	}

	public Map<String, Object> getTokenByUserCode(LoginBody loginBody, HttpServletRequest request) {
		HashMap<String, Object> dataMap = new HashMap<>();

		// 构建默认返回数据
		getDefaultData(dataMap);

		if (StringUtils.isBlank(loginBody.getUserCode())) {
//			recordLogService.recordLogininfor(loginBody.getUserCode(), Constants.LOGIN_FAIL, "userCode为空");
			dataMap.put("title", "参数异常");
			dataMap.put("msg", "【userCode为空】");
			log.error("【userCode为空】");
			return dataMap;
		}

		// 解密 userCode 私有
		String rsaPrivateKey = Convert.toStr(redisService.getCacheObject(Constants.RSA_PRIVATEKEY));
		if (StringUtils.isBlank(rsaPrivateKey)) {
			throw new ServiceException("【rsa-privateKey 私钥钥没有配置】");
		}

		try {
			RSAPrivateKey privateKey = RSAUtils.getPrivateKey(rsaPrivateKey);
			String userCode = RSAUtils.privateDecrypt(loginBody.getUserCode(), privateKey);
			loginBody.setUserCode(userCode);
		} catch (Exception e) {
			log.error("【解密userCode异常】", e);
			dataMap.put("title", "解密异常");
			dataMap.put("msg", "加密数据" + loginBody.getUserCode() + "解密异常");
			log.error("【解密userCode异常】，{}", loginBody.getUserCode());
			return dataMap;
		}


		// 获取用户信息
		R<LoginUser> userResult = remoteUserService.getUserByUserCode(loginBody.getUserCode());
		// 检查用户是否存在
		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
//			recordLogService.recordLogininfor(loginBody.getUserCode(), Constants.LOGIN_FAIL, "CAS外部系统访问-登录用户不存在");
			dataMap.put("title", "身份异常");
			dataMap.put("msg", "【身份为:" + loginBody.getUserCode() + " 的用户不存在】");
			log.error("【身份为:{} 的用户不存在】", loginBody.getUserCode());
			return dataMap;
		}


		LoginUser userInfo = userResult.getData();
		SysUser sysUser = userResult.getData().getSysUser();


		String token = SecurityUtils.getToken(request); // 使用的token
		if (StringUtils.isNotBlank(token)) {
			// 解析 token 数据  进行刷新时间
			LoginUser loginUser = tokenService.verifyToken(token);

			if (loginUser != null && loginUser.getUserid().equals(sysUser.getUserId())) {
				dataMap.put("expires_in", CacheConstants.EXPIRATION);
				dataMap.put("access_token", token);
				dataMap.put("title", "身份合法");
				dataMap.put("msg", "引导至首页");
				dataMap.put("userId", loginUser.getUserid());
				dataMap.put("userName", loginUser.getUsername());
				return dataMap;
			}
		}



		log.info("【LoginUser】{}", userInfo.toString());

		if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "CAS外部系统访问-对不起，您的账号已被删除");
			dataMap.put("title", "账号回收");
			dataMap.put("msg", "【对不起，您的账号：" + sysUser.getUserName() + " 已被删除】");
			log.error("【对不起，您的账号：{} 已被删除】", sysUser.getUserName());
			return dataMap;
		}
		if (UserStatus.DISABLE.getCode().equals(sysUser.getUserName())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "CAS外部系统访问-用户已停用，请联系管理员");
			dataMap.put("title", "账号停用");
			dataMap.put("msg", "【对不起，您的账号：" + sysUser.getUserName() + " 已停用】");
			log.error("【对不起，您的账号：{} 已停用，请联系管理员】", sysUser.getUserName());
			return dataMap;
		}
		// 检查IP黑名单
		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
			dataMap.put("title", "IP限制");
			dataMap.put("msg", "【很遗憾，访问IP已被列入系统黑名单】");
			log.error("【很遗憾，访问IP已被列入系统黑名单】");
			return dataMap;
		}

		// 检查登录人数限制
		String loginNumberStr = configService.getConfigKey2("loginNumber", SecurityConstants.INNER).get("msg").toString();
		String userWhite = configService.getConfigKey2("user_white_list", SecurityConstants.INNER).get("msg").toString();
		List<String> userWhiteList = new ArrayList<>();
		if (StringUtils.isNotBlank(userWhite)) {
			// 使用Stream API将字符串转换为List
			userWhiteList = Arrays.stream(userWhite.split(","))
					.collect(Collectors.toList());
		}
		//0则是没有人数限制
		if (!("0".equals(loginNumberStr)) && !(userWhiteList.contains(sysUser.getUserName()))) {
			List<SysUserOnline> sysUserOnlines = remoteUserOnlineService.listAll(null, null, SecurityConstants.INNER);
			List<SysUserOnline> toRemove = new ArrayList<>();
			sysUserOnlines.forEach(sysUserOnline -> {
				if (sysUserOnline.getUserName().equals(sysUser.getUserName())) {
					toRemove.add(sysUserOnline);
				}
			});
			sysUserOnlines.removeAll(toRemove);

			if (StringUtils.isNotBlank(loginNumberStr)) {
				int uniqueUserCount = (int) sysUserOnlines.stream()
						.map(SysUserOnline::getUserName)
						.collect(Collectors.toCollection(HashSet::new)) // 使用HashSet自动去重
						.size();
				int loginNumber = Integer.parseInt(loginNumberStr);
				if ((uniqueUserCount + 1) > loginNumber) {
					dataMap.put("title", "在线人数限制");
					dataMap.put("msg", "【当前访问人数较多或网络不佳，请稍后再试】");
					log.error("【当前访问人数较多或网络不佳，请稍后再试】");
					return dataMap;
				}
			}
		}

		//  检查体验用户校验是否过期-过期角色变为游客
		checkUserTrialExpiration(userInfo.getRoles(), sysUser);

		// 创建token
		Map<String, Object> serviceToken = tokenService.createToken(userInfo);
		dataMap.putAll(serviceToken);
		dataMap.put("title", "身份合法");
		dataMap.put("msg", "引导至首页");
		recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS, "CAS外部系统访问成功");

		return dataMap;
	}

	private void getDefaultData(HashMap<String, Object> dataMap) {
		List<SysDictData> sysDictDatas = remoteDictTypeService.dictTypeGetInfo(Constants.SSO_LOGIN_PARAMETERS, SecurityConstants.INNER);
		if (sysDictDatas.isEmpty()) {
			throw new ServiceException("【字典数据异常" + Constants.SSO_LOGIN_PARAMETERS + "没有配置】");
		}

		// 使用 Stream API 将 List 转换为 Map
		Map<String, SysDictData> dictDataMap = sysDictDatas.stream()
				.collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
		SysDictData sysDictData = dictDataMap.get(Constants.LOGINREDIRECTURL);

		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.LOGINREDIRECTURL + "没有配置】");
		}
		dataMap.put(Constants.LOGINREDIRECTURL, dictDataMap.get(Constants.LOGINREDIRECTURL).getDictValue());

		sysDictData = dictDataMap.get(Constants.AICAILOGINURL);
		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.AICAILOGINURL + "没有配置】");
		}
		dataMap.put(Constants.AICAILOGINURL, dictDataMap.get(Constants.AICAILOGINURL).getDictValue());

		sysDictData = dictDataMap.get(Constants.AICAICASLOGINURL);
		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.AICAICASLOGINURL + "没有配置】");
		}
		dataMap.put(Constants.AICAICASLOGINURL, dictDataMap.get(Constants.AICAICASLOGINURL).getDictValue());
		sysDictData = dictDataMap.get(Constants.AICAICASLOGINURL);

		sysDictData = dictDataMap.get(Constants.CASLOGOUT);
		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.CASLOGOUT + "没有配置】");
		}
		dataMap.put(Constants.CASLOGOUT, dictDataMap.get(Constants.CASLOGOUT).getDictValue());

		sysDictData = dictDataMap.get(Constants.ROUTE_PPT_V1);
		if (StringUtils.isNull(sysDictData)) {
			log.warn("【字典数据异常" + Constants.ROUTE_PPT_V1 + "没有配置】");
			dataMap.put(Constants.ROUTE_PPT_V1, "");
		} else {
			dataMap.put(Constants.ROUTE_PPT_V1, dictDataMap.get(Constants.ROUTE_PPT_V1).getDictValue());
		}
	}

	public String casLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
		LoginUser loginUser = tokenService.getLoginUser();
		log.info("{} - {} - 进行CAS 退出", loginUser.getUsername(), loginUser.getSysUser().getNickName());

		List<SysDictData> sysDictDatas = remoteDictTypeService.dictTypeGetInfo(Constants.SSO_LOGIN_PARAMETERS, SecurityConstants.INNER);
		if (sysDictDatas.isEmpty()) {
			throw new ServiceException("【字典数据异常" + Constants.SSO_LOGIN_PARAMETERS + "没有配置】");
		}

		// 使用 Stream API 将 List 转换为 Map
		Map<String, SysDictData> dictDataMap = sysDictDatas.stream()
				.collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
		SysDictData sysDictData = dictDataMap.get(Constants.LOGINREDIRECTURL);

		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.SHANCAICASLOGOUT + "没有配置】");
		}
		// cas 退出
		String casLogoutURL = dictDataMap.get(Constants.CASLOGOUT).getDictValue();

		sysDictData = dictDataMap.get(Constants.LOGINREDIRECTURL);

		if (StringUtils.isNull(sysDictData)) {
			throw new ServiceException("【字典数据异常" + Constants.LOGINREDIRECTURL + "没有配置】");
		}

		// 退出后重定向的服务地址（应该是应用的登录页面），需要进行URL编码
		String serviceUrl = dictDataMap.get(Constants.LOGINREDIRECTURL).getDictValue();

		// 本系统退出
		String token = SecurityUtils.getToken(request);
		if (StringUtils.isNotEmpty(token)) {
			try {
				String username = JwtUtils.getUserName(token);
				// 删除用户缓存记录
				AuthUtil.logoutByToken(token);
				// 记录用户退出日志
				sysLoginService.logout(username);
			} catch (Exception e) {
				log.error("本系统退出异常", e);
			}
		}

		// 完成CAS认证退出
		// 使当前会话无效，即退出当前用户
		request.getSession().invalidate();

		// 使用标准字符集进行 URL 编码
		String encodedServiceUrl = URLEncoder.encode(serviceUrl, StandardCharsets.UTF_8.toString());

		// 构造包含编码后的 service 参数的重定向 URL
		String redirectURL = casLogoutURL + "?service=" + encodedServiceUrl;
		// 重定向到 CAS 系统的登出接口
//		response.sendRedirect(redirectURL);
		return casLogoutURL + "?service=" + encodedServiceUrl;
	}

	public LoginUser loginByPhoneNumber(String phoneNumber, String code) {
		// 用户名或密码为空 错误
		if (StringUtils.isAnyBlank(phoneNumber, code)) {
			recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "手机号/验证码必须填写");
			throw new ServiceException("手机号/验证码必须填写");
		}
		if (!redisService.hasKey("DL-" + phoneNumber)) {
			throw new ServiceException("验证码失效或未发送");
		}
		String cacheObject = redisService.getCacheObject("DL-" + phoneNumber).toString();
		if (!code.equals(cacheObject)) {
			throw new ServiceException("验证码错误");
		}

		// IP黑名单校验
		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
			recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
			throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
		}
		// 查询用户信息
		R<LoginUser> userResult = remoteUserService.getUserInfoByPhoneNumber(phoneNumber);

		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
			recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "登录用户不存在");
			throw new ServiceException("登录用户：" + phoneNumber + " 不存在");
		}

		if (R.FAIL == userResult.getCode()) {
			throw new ServiceException(userResult.getMsg());
		}

		LoginUser userInfo = userResult.getData();
		SysUser user = userResult.getData().getSysUser();
		if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
			recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
			throw new ServiceException("对不起，您的账号：" + phoneNumber + " 已被删除");
		}
		if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
			recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
			throw new ServiceException("对不起，您的账号：" + phoneNumber + " 已停用");
		}

		recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_SUCCESS, "登录成功");

		// 清除验证码缓存
		redisService.deleteObject("DL-" + phoneNumber);
		return userInfo;
	}

//	public void casLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
//		// CAS 服务认证后带来的数据
//		String uid = request.getRemoteUser();
//		log.info("uid: {}", uid);
//		Principal principal = request.getUserPrincipal();
//		log.info("principal: {}", principal);
//		if (principal instanceof AttributePrincipal) {
//			AttributePrincipal aPrincipal = (AttributePrincipal) principal;
//			//获取用户信息中公开的Attributes部分
//			Map<String, Object> map = aPrincipal.getAttributes();
//			log.info("map: {}", map);
//			// {isFromNewLogin=false, authenticationDate=2024-12-04T11:11:04.387+08:00[GMT+08:00],
//			// loginType=1, successfulAuthenticationHandlers=com.wisedu.minos.config.login.RememberMeUsernamePasswordHandler,
//			// ip=***********, cn=程建, USER_LOGIN_DATE=Wed Dec 04 11:11:04 GMT+08:00 2024,
//			// userName=程建, ua=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36,
//			// samlAuthenticationStatementAuthMethod=urn:oasis:names:tc:SAML:1.0:am:unspecified,
//			// credentialType=MyRememberMeCaptchaCredential, uid=test002,
//			// authenticationMethod=com.wisedu.minos.config.login.RememberMeUsernamePasswordHandler,
//			// longTermAuthenticationRequestTokenUsed=false,
//			// containerId=ou=1000003,ou=People, cllt=userNameLogin,
//			// dllt=generalLogin, USER_LOGIN_TYPE=2}
//			// 获取姓名,可以根据属性名称获取其他属性
////			cn = (String) map.get("cn");
//		}
//
//		// 设置要传递的参数
//		String userCode = "";
//		String title = "";
//		String msg = "";
//		String backUrl = "https://aicai.sdufe.edu.cn/ssoLogin";
//		String shanCaiLoginRedirectUrl = "";
//		String aiCaiLoginUrl = "";
//
//		List<SysDictData> sysDictDatas = remoteDictTypeService.dictTypeGetInfo(Constants.SSO_LOGIN_PARAMETERS, SecurityConstants.INNER);
//		if (sysDictDatas.isEmpty()) {
//			throw new ServiceException("【字典数据异常" + Constants.SSO_LOGIN_PARAMETERS + "没有配置】");
//		}
//
//		// 使用 Stream API 将 List 转换为 Map
//		Map<String, SysDictData> dictDataMap = sysDictDatas.stream()
//				.collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
//		SysDictData sysDictData = dictDataMap.get(Constants.SHANCAILOGINREDIRECTURL);
//
//		if (StringUtils.isNull(sysDictData)) {
//			throw new ServiceException("【字典数据异常" + Constants.SHANCAILOGINREDIRECTURL + "没有配置】");
//		}
//		shanCaiLoginRedirectUrl = String.valueOf(dictDataMap.get(Constants.AICAILOGINURL));
//
//		sysDictData = dictDataMap.get(Constants.AICAILOGINURL);
//		if (StringUtils.isNull(sysDictData)) {
//			throw new ServiceException("【字典数据异常" + Constants.AICAILOGINURL + "没有配置】");
//		}
//		aiCaiLoginUrl = String.valueOf(dictDataMap.get(Constants.AICAILOGINURL));
//
//		if (StringUtils.isBlank(uid)) {
//			// 为空 没有通过CAS认证
//			title = "参数异常";
//			msg = "【userCode 为空】";
//			// 对中文进行 URL 编码
//			String encodedTitle = URLEncoder.encode(title, "UTF-8");
//			String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//			// 构建 URL，包含编码后的参数
//			String url = backUrl + "?" +
//					"userCode=" + userCode +
//					"&title=" + encodedTitle +
//					"&msg=" + encodedMsg +
//					"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//					"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//			response.sendRedirect(url);
//		}
//
//		// 认证通过 确认本系统用户
//		// 获取用户信息
//		userCode = uid;
//		R<LoginUser> userResult = remoteUserService.getUserByUserCode(userCode);
//		// 检查用户是否存在
//		if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
//			recordLogService.recordLogininfor(userCode, Constants.LOGIN_FAIL, "外部系统访问-登录用户不存在");
//			title = "用户信息缺失";
//			msg = "【身份(学号或者工号)为" + userCode + " 的用户不存在】";
//			// 对中文进行 URL 编码
//			String encodedTitle = URLEncoder.encode(title, "UTF-8");
//			String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//			// 构建 URL，包含编码后的参数
//			String url = backUrl + "?" +
//					"userCode=" + userCode +
//					"&title=" + encodedTitle +
//					"&msg=" + encodedMsg +
//					"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//					"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//			response.sendRedirect(url);
//		}
//
//		// 系统用户
//		LoginUser loginUser = userResult.getData();
//		SysUser sysUser = userResult.getData().getSysUser();
//
//		if (UserStatus.DELETED.getCode().equals(sysUser.getDelFlag())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "外部系统访问-对不起，您的账号已被删除");
//			title = "账号删除";
//			msg = "【对不起，您的账号：" + sysUser.getUserName() + " 已被删除】";
//			// 对中文进行 URL 编码
//			String encodedTitle = URLEncoder.encode(title, "UTF-8");
//			String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//			// 构建 URL，包含编码后的参数
//			String url = backUrl + "?" +
//					"userCode=" + userCode +
//					"&title=" + encodedTitle +
//					"&msg=" + encodedMsg +
//					"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//					"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//			response.sendRedirect(url);
//		}
//		if (UserStatus.DISABLE.getCode().equals(sysUser.getUserName())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "外部系统访问-用户已停用，请联系管理员");
//			title = "账号停用";
//			msg = "【对不起，您的账号：" + sysUser.getUserName() + " 已停用】";
//			// 对中文进行 URL 编码
//			String encodedTitle = URLEncoder.encode(title, "UTF-8");
//			String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//			// 构建 URL，包含编码后的参数
//			String url = backUrl + "?" +
//					"userCode=" + userCode +
//					"&title=" + encodedTitle +
//					"&msg=" + encodedMsg +
//					"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//					"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//			response.sendRedirect(url);
//		}
//
//		// IP黑名单校验
//		String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
//		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
//			recordLogService.recordLogininfor(sysUser.getUserName(), Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
//			title = "禁止访问";
//			msg = "【很遗憾，访问IP已被列入系统黑名单】";
//			// 对中文进行 URL 编码
//			String encodedTitle = URLEncoder.encode(title, "UTF-8");
//			String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//			// 构建 URL，包含编码后的参数
//			String url = backUrl + "?" +
//					"userCode=" + userCode +
//					"&title=" + encodedTitle +
//					"&msg=" + encodedMsg +
//					"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//					"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//			response.sendRedirect(url);
//		}
//
//		// 检查登录人数限制
//		String loginNumberStr = configService.getConfigKey2("loginNumber", SecurityConstants.INNER).get("msg").toString();
//		String userWhite = configService.getConfigKey2("user_white_list", SecurityConstants.INNER).get("msg").toString();
//		List<String> userWhiteList = new ArrayList<>();
//		if (StringUtils.isNotBlank(userWhite)) {
//			// 使用Stream API将字符串转换为List
//			userWhiteList = Arrays.stream(userWhite.split(","))
//					.collect(Collectors.toList());
//		}
//		//0则是没有人数限制
//		if (!("0".equals(loginNumberStr)) && !(userWhiteList.contains(sysUser.getUserName()))) {
//			List<SysUserOnline> sysUserOnlines = remoteUserOnlineService.listAll(null, null, SecurityConstants.INNER);
//			List<SysUserOnline> toRemove = new ArrayList<>();
//			sysUserOnlines.forEach(sysUserOnline -> {
//				if (sysUserOnline.getUserName().equals(sysUser.getUserName())) {
//					toRemove.add(sysUserOnline);
//				}
//			});
//			sysUserOnlines.removeAll(toRemove);
//
//			if (StringUtils.isNotBlank(loginNumberStr)) {
//				int uniqueUserCount = (int) sysUserOnlines.stream()
//						.map(SysUserOnline::getUserName)
//						.collect(Collectors.toCollection(HashSet::new)) // 使用HashSet自动去重
//						.size();
//				int loginNumber = Integer.parseInt(loginNumberStr);
//				if ((uniqueUserCount + 1) > loginNumber) {
//					title = "人数限制";
//					msg = "【当前访问人数较多或网络不佳，请稍后再试】";
//					// 对中文进行 URL 编码
//					String encodedTitle = URLEncoder.encode(title, "UTF-8");
//					String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//					// 构建 URL，包含编码后的参数
//					String url = backUrl + "?" +
//							"userCode=" + userCode +
//							"&title=" + encodedTitle +
//							"&msg=" + encodedMsg +
//							"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//							"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//					response.sendRedirect(url);
//				}
//			}
//		}
//
//		//  检查体验用户校验是否过期-过期角色变为游客
//		checkUserTrialExpiration(loginUser.getRoles(), sysUser);
//
//		title = "身份合法";
//		msg = "【引导至首页】";
//
//		Map<String, Object> serviceToken = tokenService.createToken(loginUser);
//		// 对中文进行 URL 编码
//		String encodedTitle = URLEncoder.encode(title, "UTF-8");
//		String encodedMsg = URLEncoder.encode(msg, "UTF-8");
//		// 构建 URL，包含编码后的参数
//		String url = backUrl + "?" +
//				"userCode=" + userCode +
//				"&title=" + encodedTitle +
//				"&msg=" + encodedMsg +
//				"&shanCaiLoginRedirectUrl=" + shanCaiLoginRedirectUrl +
//				"&aiCaiLoginUrl=" + aiCaiLoginUrl;
//		response.sendRedirect(url);
//	}
}
