package com.ruoyi.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.savedrequest.NoOpServerRequestCache;
import org.springframework.security.web.server.util.matcher.PathPatternParserServerWebExchangeMatcher;
import org.springframework.security.web.server.util.matcher.OrServerWebExchangeMatcher;
import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatcher;

import java.util.Arrays;

/**
 * Swagger安全配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebFluxSecurity
public class SwaggerSecurityConfig {
    
    @Value("${spring.security.swagger.user.name:swagger}")
    private String username;
    
    @Value("${spring.security.swagger.user.password:swagger}")
    private String password;
    
    @Value("${spring.security.swagger.user.roles:SWAGGER}")
    private String roles;

    /**
     * 配置Swagger相关端点的访问安全
     * 
     * @param http ServerHttpSecurity对象
     * @return SecurityWebFilterChain
     */
    @Bean
    public SecurityWebFilterChain swaggerSecurityFilterChain(ServerHttpSecurity http) {
        // 创建多个路径匹配器
        ServerWebExchangeMatcher[] matchers = {
            new PathPatternParserServerWebExchangeMatcher("/swagger-resources/**"),
            new PathPatternParserServerWebExchangeMatcher("/swagger-ui/**"),
            new PathPatternParserServerWebExchangeMatcher("/v2/api-docs/**"),
            new PathPatternParserServerWebExchangeMatcher("/webjars/springfox-swagger-ui/**")
        };
        // 组合为一个OR匹配器
        ServerWebExchangeMatcher swaggerMatcher = new OrServerWebExchangeMatcher(Arrays.asList(matchers));
        
        return http
                .securityMatcher(swaggerMatcher)
                .authorizeExchange()
                .anyExchange().authenticated()
                .and()
                .httpBasic()
                .and()
                .requestCache()
                .requestCache(NoOpServerRequestCache.getInstance())
                .and()
                .csrf().disable()
                .build();
    }
    
    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 配置用户详情服务
     * 
     * @return MapReactiveUserDetailsService
     */
    @Bean
    public MapReactiveUserDetailsService userDetailsService() {
        // 创建Swagger API文档访问用户
        UserDetails swaggerUser = User.builder()
                .username(username)
                .password(passwordEncoder().encode(password))
                .roles(roles)
                .build();
        
        return new MapReactiveUserDetailsService(swaggerUser);
    }
} 