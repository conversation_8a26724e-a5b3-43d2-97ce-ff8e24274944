package com.ruoyi.gateway.config;

import org.springframework.boot.actuate.autoconfigure.security.reactive.EndpointRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.savedrequest.NoOpServerRequestCache;

/**
 * Actuator安全配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebFluxSecurity
public class ActuatorSecurityConfig {

    /**
     * 配置Actuator端点的访问安全
     * 
     * @param http ServerHttpSecurity对象
     * @return SecurityWebFilterChain
     */
    @Bean
    public SecurityWebFilterChain actuatorSecurityFilterChain(ServerHttpSecurity http) {
        return http
                .securityMatcher(EndpointRequest.toAnyEndpoint())
                .authorizeExchange()
                // 只有健康检查端点可以公开访问
                .pathMatchers("/actuator/health").permitAll()
                // 其他actuator端点需要认证才能访问
                .anyExchange().authenticated()
                .and()
                .httpBasic()
                .and()
                .requestCache()
                .requestCache(NoOpServerRequestCache.getInstance())
                .and()
                .csrf().disable()
                .build();
    }
} 