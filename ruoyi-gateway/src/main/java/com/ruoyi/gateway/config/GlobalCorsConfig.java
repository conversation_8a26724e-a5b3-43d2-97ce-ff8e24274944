package com.ruoyi.gateway.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

/**
 * @BelongsProject: large-model-end
 * @BelongsPackage: com.ruoyi.gateway.config
 * @Author: zhao_tian_qi
 * @CreateTime: 2025-01-03  10:32
 * @Description:
 * @Version: 1.0
 */
@Configuration
public class GlobalCorsConfig {

	@Bean
	public CorsWebFilter corsWebFilter() {
		CorsConfiguration config = new CorsConfiguration();
		// 这里仅为了说明问题，配置为放行所有域名，生产环境请对此进行修改
		config.addAllowedOriginPattern("*");
		// 放行的请求头
		config.addAllowedHeader("*");
		// 放行的请求类型，有GET,POST,PUT,DELETE,OPTIONS
		config.addAllowedMethod("*");
		// 暴露头部信息
		config.addExposedHeader("*");
		// 是否允许发送Cookie
		config.setAllowCredentials(true);

		// 需要将CorsConfigurationSource 类型明确为 CorsConfigurationSource
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", config);

		return new CorsWebFilter(source);
	}
}
