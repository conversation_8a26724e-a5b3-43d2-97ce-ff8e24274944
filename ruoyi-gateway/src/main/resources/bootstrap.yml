# Tomcat
server:
  port: 8080

jasypt:
  encryptor:
    # 加密英子 自定义随机字符串
    password: 3b44347asdqwe279a53a3abb1fmklqwe
    # 加密算法
    algorithm: PBEWithHmacSHA512AndAES_128

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: <PERSON><PERSON>(BAeypRZaKzxBH3VhI2G+wMTh0Zj8wMVdVwIdbC1ZYysgk6pCqjIy9IVupOFXjtHK)
        username: <PERSON><PERSON>(WK/JdhC8/dG6tQsYx8CJZ15uv1VCLoxLtyoKAIHsA5EjFkk4Y8bTX4upHDyqmZJU)
        password: E<PERSON>(lbKaLQQJ8UNvypczCwk5fwwyGC8ms6UrO+xiqDTtOGKrpeB+Yi/nvrBQZIg3DwaU)
      config:
        # 配置中心地址
        server-addr: ENC(BAeypRZaKzxBH3VhI2G+wMTh0Zj8wMVdVwIdbC1ZYysgk6pCqjIy9IVupOFXjtHK)
        username: ENC(WK/JdhC8/dG6tQsYx8CJZ15uv1VCLoxLtyoKAIHsA5EjFkk4Y8bTX4upHDyqmZJU)
        password: ENC(lbKaLQQJ8UNvypczCwk5fwwyGC8ms6UrO+xiqDTtOGKrpeB+Yi/nvrBQZIg3DwaU)
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: ENC(XNT+ncEEjSZF/UDzkiC5Xk96bDcmuCTZXhWQyI8f5zfzin3dtBpWuRyMSyJQ5ZeC)
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ENC(BAeypRZaKzxBH3VhI2G+wMTh0Zj8wMVdVwIdbC1ZYysgk6pCqjIy9IVupOFXjtHK)
            username: ENC(WK/JdhC8/dG6tQsYx8CJZ15uv1VCLoxLtyoKAIHsA5EjFkk4Y8bTX4upHDyqmZJU)
            password: ENC(lbKaLQQJ8UNvypczCwk5fwwyGC8ms6UrO+xiqDTtOGKrpeB+Yi/nvrBQZIg3DwaU)
            dataId: sentinel-ruoyi-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
  # 安全配置
  security:
  # actuator
    user:
      name: admin
      password: actuatorCoffee#@ADMin
      roles: admin
    # Swagger访问安全配置
    swagger:
      user:
        name: admin
        password: swaggerCoffee#@ADMin
        roles: admin

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        # 暴露监控端点
        include: '*'
      # 设置基础路径
      base-path: /actuator
  endpoint:
    health:
      # 显示详细健康信息
      show-details: always
    # 启用shutdown端点（慎用）
    shutdown:
      enabled: false
